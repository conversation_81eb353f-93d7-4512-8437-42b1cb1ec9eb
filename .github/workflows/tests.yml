name: Tests

on:
  pull_request:
  workflow_dispatch:
  push:
    branches:
      - development
concurrency:
  group: ${{ github.event_name == 'pull_request' && github.event.pull_request.number || 'development' }}-tests
  cancel-in-progress: true

env:
  RAILS_ENV: test
  BUNDLE_GEMFILE: ${{ github.workspace }}/Gemfile
  BUNDLE_RUBYGEMS__PKG__GITHUB__COM: MDReleaseBot:****************************************
  GIT_BRANCH: ${{ github.event_name == 'pull_request' && github.ref_name || 'development' }}
  GIT_COMMIT_SHA: ${{ github.event_name == 'pull_request' && github.event.pull_request.head.sha || github.sha }}
  SECRET_KEY_BASE_DUMMY: 1

permissions:
  contents: read
  packages: read
  pull-requests: write
  statuses: write
  id-token: write

jobs:
  rubocop:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-22.04
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        env:
          BUNDLE_WITH: test
        with:
          ruby-version: 3.2.2
          bundler-cache: true
      - name: Run Pronto
        env:
          PRONTO_PULL_REQUEST_ID: ${{ github.event.pull_request.number }}
          PRONTO_GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          bundle exec pronto run --exit-code --formatters text github_status github_pr --commit "origin/$GITHUB_BASE_REF"
  frontend_lint:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-22.04
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
          cache-dependency-path: yarn.lock
      - name: Yarn install
        run: yarn install --frozen-lockfile
      - name: Fetch the base branch, so we can use `git diff`
        run: git fetch origin refs/heads/${{ github.event.pull_request.base.ref }}:refs/heads/${{ github.event.pull_request.base.ref }}
      - name: Run lint
        env:
          ESLINT_PLUGIN_DIFF_COMMIT: ${{ github.event.pull_request.base.ref }}
        run: yarn lint
  scss_modules:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-22.04
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
          cache-dependency-path: yarn.lock
      - name: Yarn install
        run: yarn install --frozen-lockfile
      - name: Run TSM
        run: yarn tsm --listDifferent
  storybook:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
          cache-dependency-path: yarn.lock
      - name: Yarn install
        run: yarn install --frozen-lockfile
      - name: Build the storybook
        run: yarn storybook:build
  brakeman:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-22.04
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        env:
          BUNDLE_WITH: test
        with:
          ruby-version: 3.2.2
          bundler-cache: true
      - name: Check Brakeman
        run: ./script/brakeman_check.rb 34
  yamllint:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-22.04
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          base: ${{ github.event.pull_request.base.ref }}
          filters: |
            changes:
              - 'config/locales/**/*.{yml,yaml}'
      - name: 'Yamllint'
        uses: karancode/yamllint-github-action@v3.0.0
        if: steps.filter.outputs.changes == 'true'
        with:
          yamllint_config_filepath: ${{ github.workspace }}/.yamllint
          yamllint_file_or_dir: ${{ github.workspace }}/config/locales
          yamllint_format: github
          yamllint_strict: false
          yamllint_comment: true
        env:
          GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  yarn:
    runs-on: ubuntu-22.04
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
          cache-dependency-path: yarn.lock
      - name: Yarn install
        run: yarn install --frozen-lockfile
      - name: Javascript Tests
        run: yarn run test --silent --coverage
      - name: Upload code coverage for matrix element
        uses: actions/upload-artifact@v4
        with:
          name: coverage.yarn
          path: coverage-final.json
  rspec:
    runs-on: rspec-gcp
    timeout-minutes: 45
    env:
      HOME: "/root"
    services:
      mysql:
        image: europe-west2-docker.pkg.dev/market-dojo-development/deps/mysql:8.4
        credentials:
          username: _json_key
          password: ${{ secrets.DEVELOPMENT_SERVICE_ACCOUNT_KEY }}
        env:
          MYSQL_ROOT_PASSWORD: root
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      redis:
        image: europe-west2-docker.pkg.dev/market-dojo-development/deps/redis:latest
        credentials:
          username: _json_key
          password: ${{ secrets.DEVELOPMENT_SERVICE_ACCOUNT_KEY }}
        ports:
          - 6379:6379
      elasticsearch:
        image: europe-west2-docker.pkg.dev/market-dojo-development/deps/elasticsearch:6.8.23
        credentials:
          username: _json_key
          password: ${{ secrets.DEVELOPMENT_SERVICE_ACCOUNT_KEY }}
        ports:
          - 9200:9200
          - 9300:9300
        options: -e="discovery.type=single-node" --health-cmd="curl http://localhost:9200/_cluster/health" --health-interval=10s --health-timeout=5s --health-retries=10
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - run: npm install -g yarn
      - name: Yarn install
        run: yarn install
      - name: Cache assets
        id: cache-assets
        uses: everpcpc/actions-cache@v2
        with:
          provider: gcs
          bucket: "market-dojo-development-gh-runner-cache"
          root: "cloud/cache"
          path: ${{ github.workspace }}/public/assets
          key: ${{ runner.os }}-assets-${{ hashFiles('app/assets/**/*') }}
          restore-keys: |
            ${{ runner.os }}-assets
      - name: Cache Webpack output
        uses: everpcpc/actions-cache@v2
        with:
          provider: gcs
          bucket: "market-dojo-development-gh-runner-cache"
          root: "cloud/cache"
          path: ${{ github.workspace }}/public/packs
          key: ${{ runner.os }}-packs-${{ hashFiles('app/javascript/**/*') }}
          restore-keys: |
            ${{ runner.os }}-packs
      - name: Cache test runtimes
        uses: everpcpc/actions-cache@v2
        with:
          provider: gcs
          bucket: "market-dojo-development-gh-runner-cache"
          root: "cloud/cache"
          path: ${{ github.workspace }}/log/parallel_runtime_rspec.log
          key: ${{ runner.os }}-runtime-rspec
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        env:
          BUNDLE_WITH: test
        with:
          ruby-version: 3.2.2
          bundler-cache: true
      - name: Copy config
        run: cp ${{ github.workspace }}/.github/deps/database.yml ${{ github.workspace }}/config/database.yml
      - name: Prepare environment
        env:
          FIXTURES_PATH: spec/fixtures
        run: |
          mkdir tmp
          echo $(ruby -e "require 'securerandom'; puts SecureRandom.hex(64)") > tmp/local_secret.txt
          bundle exec rake parallel:setup parallel:rake[db:fixtures:load]
      - name: Precompile assets
        if: steps.cache-assets.outputs.cache-hit != 'true'
        run: |
          bundle exec rake assets:precompile
      - name: Run RSpecs
        env:
          IGNORE_SWITCHES: 1
          EAGER_LOAD: 1
          SKIP_BULLET: 1
          RAILS_DISABLE_TEST_LOG: 1
          ELASTICSEARCH_URL: 127.0.0.1
        run: |
          mkdir -p tmp log
          bundle exec parallel_test ${{ github.workspace }}/spec -n $(nproc) --runtime-log log/parallel_runtime_rspec.log --type rspec --verbose
      - name: Upload code coverage for matrix element
        uses: actions/upload-artifact@v4
        with:
          name: coverage.rspec
          path: coverage/coverage.json
  coverage:
    runs-on: ubuntu-22.04
    timeout-minutes: 15
    needs: ["rspec", "yarn"]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - uses: actions/download-artifact@v4
      - uses: qltysh/qlty-action/coverage@v1
        with:
          oidc: true
          strip-prefix: "/workspace/auction/auction/"
          files: coverage.rspec/coverage.json,coverage.yarn/coverage-final.json
