GIT
  remote: https://github.com/JangoSteve/remotipart.git
  revision: a1860d64aef484047bc1ef50e2f19ef2480eacf9
  specs:
    remotipart (1.4.4)

GIT
  remote: https://github.com/felipevenancio/simple_enum.git
  revision: d11476a9bab7b37b07d71c01b2b8fe8d0882b94d
  specs:
    simple_enum (2.4.0)
      activesupport (>= 4.0.0)

GIT
  remote: https://github.com/lynndylanhurley/devise_token_auth
  revision: 812711e4776f2eab2de8a057bd5befc023fb666b
  specs:
    devise_token_auth (1.2.3)
      bcrypt (~> 3.0)
      devise (> 3.5.2, < 5)
      rails (>= 4.2.0, < 7.3)

GIT
  remote: https://github.com/marketdojo/acts-as-taggable-on.git
  revision: f35c71d0f119dbdbb6f3939563bd79e1d38c0e30
  branch: rails7
  specs:
    acts-as-taggable-on (6.5.1)
      activerecord (>= 5.0, < 10.1)

GIT
  remote: https://github.com/marketdojo/declarative_authorization.git
  revision: 6f3e81f7c8b0f6c965b23d5ee90eceabdca1cfb4
  branch: allow_rails_7
  specs:
    declarative_authorization (1.3.0)
      rails (>= 4.1.0, < 8)
      ruby_parser (>= 3.6.6)

GIT
  remote: https://github.com/marketdojo/dynamic_form
  revision: adb0a03601311db8a28598f921b5315f266ebfc9
  specs:
    dynamic_form (1.1.4)

GIT
  remote: https://github.com/marketdojo/henkei.git
  revision: 0e8b2df4e84516dc959554b9bf69f2e67af6a3bc
  branch: tesseract
  specs:
    henkei (1.22.0)
      json (>= 1.8, < 3)
      mime-types (>= 1.23, < 4)

GIT
  remote: https://github.com/marketdojo/omniauth-azure-oauth2.git
  revision: 6978057a0cedef7645a515d6c191e2cf10b04ff9
  specs:
    omniauth-azure-oauth2 (0.0.10)
      jwt (>= 1.0, < 3.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.4)

GIT
  remote: https://github.com/marketdojo/zcrm-ruby-sdk.git
  revision: 5b43f2d047677b4299dfc3f6dc7ded02cae774ea
  branch: master
  specs:
    ZCRMSDK (1.0.2)
      json (~> 2.2)
      multipart-post (~> 2.0)
      rest-client (~> 2.0)

GIT
  remote: https://github.com/niciliketo/acts-as-tree-with-dotted-ids.git
  revision: 79937b64a2c0fa89b894246de1965728e5c70396
  branch: rails7
  specs:
    acts-as-tree-with-dotted-ids (1.4.1)
      activerecord (>= 4.0.0, < 8.0)

GIT
  remote: https://github.com/nourishcare/globalize-versioning
  revision: 9396f19c761d48cbd26da953fa7fad4b6b522a07
  branch: main
  specs:
    globalize-versioning (0.5.0)
      activemodel (>= 4.2.0, < 8.0)
      activerecord (>= 4.2.0, < 8.0)
      globalize (>= 5.1.0, < 8)
      paper_trail (>= 8, < 16)

GIT
  remote: https://github.com/ryanb/nested_form.git
  revision: 78ad0469e681f159072e04c9c57d8722c9826295
  specs:
    nested_form (0.3.2)

GIT
  remote: https://github.com/wearevolt/memoist.git
  revision: b206d05556c0ed913a356863db84fcb3bec486cc
  specs:
    memoist (0.16.2)

PATH
  remote: vendor/engines/category_dojo
  specs:
    category_dojo (0.0.1)
      cancancan
      i18n
      rails (>= 3.2.11)
      simple_form

PATH
  remote: vendor/engines/llm
  specs:
    llm (0.1.0)
      rails (>= 7.1.5.1)

PATH
  remote: vendor/engines/questionnaire_engine
  specs:
    questionnaire_engine (0.0.1)
      rails (>= 3.2.21)

PATH
  remote: vendor/engines/sim_dojo
  specs:
    sim_dojo (0.0.1)
      rails (>= 3.2.21)

PATH
  remote: vendor/engines/srm
  specs:
    srm (0.1.0)
      cancancan

GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_model_serializers (0.10.14)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    active_record_query_trace (1.8.3)
      activerecord (>= 6.0.0)
    active_record_union (1.3.0)
      activerecord (>= 4.0)
    active_utils (3.3.19)
      activesupport (>= 4.2)
      i18n
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemerchant (1.137.0)
      activesupport (>= 4.2)
      builder (>= 2.1.2, < 4.0.0)
      i18n (>= 0.6.9)
      nokogiri (~> 1.4)
      rexml (~> 3.3, >= 3.3.4)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.3)
      activemodel (>= 5.0.0.a)
      activesupport (>= 5.0.0.a)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (1.8.1)
      activerecord (>= 4.2)
    activerecord-session_store (2.2.0)
      actionpack (>= 7.0)
      activerecord (>= 7.0)
      cgi (>= 0.3.6)
      rack (>= 2.0.8, < 4)
      railties (>= 7.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    acts_as_list (1.2.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    acts_as_tree (2.9.1)
      activerecord (>= 3.0.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    anbt-sql-formatter (0.1.2)
    annotaterb (4.15.0)
    api-pagination (6.0.0)
    app_profiler (0.4.0)
      activesupport (>= 5.2)
      rack
      stackprof (~> 0.2)
    ast (2.4.3)
    authtrail (0.7.0)
      railties (>= 7.1)
      warden
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    benchmark-ips (2.14.0)
    benchmark-malloc (0.2.0)
    benchmark-perf (0.6.0)
    benchmark-trend (0.4.0)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    bootstrap-editable-rails (0.0.9)
      railties (>= 3.1)
      sass-rails
    bourbon (7.3.0)
      thor (~> 1.0)
    braintree (4.28.0)
      builder (>= 3.2.4)
      rexml (>= 3.1.9)
    brakeman (7.0.2)
      racc
    browser (6.2.0)
    builder (3.3.0)
    bullet (8.0.7)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (12.0.0)
    cancancan (3.6.1)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-screenshot (1.0.26)
      capybara (>= 1.0, < 4)
      launchy
    capybara-select2 (1.0.1)
      capybara
      rspec
    carrierwave (2.2.6)
      activemodel (>= 5.0.0)
      activesupport (>= 5.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      mini_mime (>= 0.1.3)
      ssrf_filter (~> 1.0)
    case_transform (0.2)
      activesupport
    caxlsx (4.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.4)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    cgi (0.4.2)
    childprocess (5.1.0)
      logger (~> 1.5)
    ckeditor (4.3.0)
      orm_adapter (~> 0.5.0)
      terrapin
    climate_control (1.2.0)
    cliver (0.3.2)
    code_analyzer (0.5.5)
      sexp_processor
    coderay (1.1.3)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    countries (6.0.1)
      unaccent (~> 0.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.17.1)
      addressable
    daemons (1.4.1)
    dalli (3.2.8)
    date (3.4.1)
    dead_end (4.0.0)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.2.0)
    declarative (0.0.20)
    deep_cloneable (3.2.1)
      activerecord (>= 3.1.0, < 9)
    delayed_job (4.1.13)
      activesupport (>= 3.0, < 9.0)
    delayed_job_active_record (4.1.11)
      activerecord (>= 3.0, < 9.0)
      delayed_job (>= 3.0, < 5)
    delocalize (1.2.0)
      rails (>= 2)
    dentaku (3.5.4)
      bigdecimal
      concurrent-ruby
    derailed_benchmarks (2.2.1)
      base64
      benchmark-ips (~> 2)
      bigdecimal
      drb
      get_process_mem
      heapy (~> 0)
      logger
      memory_profiler (>= 0, < 2)
      mini_histogram (>= 0.3.0)
      mutex_m
      ostruct
      rack (>= 1)
      rack-test
      rake (> 10, < 14)
      ruby-statistics (>= 4.0.1)
      ruby2_keywords
      thor (>= 0.19, < 2)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-encryptable (0.2.0)
      devise (>= 2.1.0)
    diff-lcs (1.6.2)
    digest-crc (0.7.0)
      rake (>= 12.0.0, < 14.0.0)
    docile (1.4.0)
    domain_name (0.6.20240107)
    drb (2.2.3)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    dry-inflector (1.2.0)
    dry-logic (1.6.0)
      bigdecimal
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-types (1.8.2)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    easy_translate (0.5.1)
      thread
      thread_safe
    elasticsearch (7.4.0)
      elasticsearch-api (= 7.4.0)
      elasticsearch-transport (= 7.4.0)
    elasticsearch-api (7.4.0)
      multi_json
    elasticsearch-transport (7.4.0)
      faraday
      multi_json
    email_address (0.2.5)
      base64
      simpleidn
    encryptor (3.0.0)
    erb (5.0.1)
    erubi (1.13.1)
    erubis (2.7.0)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    event_stream_parser (1.0.0)
    exception_notification (4.6.0)
      actionmailer (>= 5.2, < 9)
      activesupport (>= 5.2, < 9)
    excon (0.112.0)
    execjs (2.10.0)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_instruments (1.2.0)
      activerecord (>= 4.0)
      factory_bot (>= 4)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-httpclient (2.0.1)
      httpclient (>= 2.2)
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    faraday-retry (2.3.2)
      faraday (~> 2.0)
    faraday-typhoeus (1.1.0)
      faraday (~> 2.0)
      typhoeus (~> 1.4)
    fast_blank (1.0.1)
    fast_jsonapi (1.5)
      activesupport (>= 4.2)
    fasterer (0.11.0)
      ruby_parser (>= 3.19.1)
    ffi (1.17.1-arm64-darwin)
    ffi (1.17.1-x86_64-darwin)
    ffi (1.17.1-x86_64-linux-gnu)
    flamegraph (0.9.5)
    fog-core (2.2.4)
      builder
      excon (~> 0.71)
      formatador (~> 0.2)
      mime-types
    fog-google (1.24.1)
      addressable (>= 2.7.0)
      fog-core (< 2.5)
      fog-json (~> 1.2)
      fog-xml (~> 0.1.0)
      google-apis-compute_v1 (~> 0.53)
      google-apis-dns_v1 (~> 0.28)
      google-apis-iamcredentials_v1 (~> 0.15)
      google-apis-monitoring_v3 (~> 0.37)
      google-apis-pubsub_v1 (~> 0.30)
      google-apis-sqladmin_v1beta4 (~> 0.38)
      google-apis-storage_v1 (>= 0.19, < 1)
      google-cloud-env (>= 1.2, < 3.0)
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.5)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (0.3.0)
    gapic-common (0.25.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      google-protobuf (>= 3.25, < 5.a)
      googleapis-common-protos (~> 1.6)
      googleapis-common-protos-types (~> 1.15)
      googleauth (~> 1.12)
      grpc (~> 1.66)
    gemini-ai (4.2.0)
      event_stream_parser (~> 1.0)
      faraday (~> 2.10)
      faraday-typhoeus (~> 1.1)
      googleauth (~> 1.8)
      typhoeus (~> 1.4, >= 1.4.1)
    get_process_mem (0.2.7)
      ffi (~> 1.0)
    gibberish (2.1.1)
    git (3.1.0)
      activesupport (>= 5.0)
      addressable (~> 2.8)
      process_executer (~> 1.3)
      rchardet (~> 1.9)
    gitlab (4.19.0)
      httparty (~> 0.20)
      terminal-table (>= 1.5.1)
    globalid (1.2.1)
      activesupport (>= 6.1)
    globalize (7.0.0)
      activemodel (>= 7.0, < 8.1)
      activerecord (>= 7.0, < 8.1)
      activesupport (>= 7.0, < 8.1)
      request_store (~> 1.0)
    google-apis-compute_v1 (0.122.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-core (0.18.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-dns_v1 (0.48.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-iamcredentials_v1 (0.24.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-monitoring_v3 (0.80.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-pubsub_v1 (0.62.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-sqladmin_v1beta4 (0.86.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-storage_v1 (0.52.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-core (1.8.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.3.1)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-cloud-error_reporting (0.44.0)
      concurrent-ruby (~> 1.1)
      google-cloud-core (~> 1.5)
      google-cloud-error_reporting-v1beta1 (~> 0.0)
      stackdriver-core (~> 1.3)
    google-cloud-error_reporting-v1beta1 (0.13.0)
      gapic-common (>= 0.25.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-errors (1.5.0)
    google-cloud-logging (2.6.0)
      concurrent-ruby (~> 1.1)
      google-cloud-core (~> 1.5)
      google-cloud-logging-v2 (>= 0.0, < 2.a)
      stackdriver-core (~> 1.3)
    google-cloud-logging-v2 (0.13.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-monitoring (1.9.2)
      google-cloud-core (~> 1.6)
      google-cloud-monitoring-dashboard-v1 (>= 0.14, < 2.a)
      google-cloud-monitoring-metrics_scope-v1 (>= 0.5, < 2.a)
      google-cloud-monitoring-v3 (>= 0.15, < 2.a)
    google-cloud-monitoring-dashboard-v1 (1.2.1)
      gapic-common (>= 0.25.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-monitoring-metrics_scope-v1 (1.2.1)
      gapic-common (>= 0.25.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-monitoring-v3 (0.18.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-storage (1.56.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-core (~> 0.13)
      google-apis-iamcredentials_v1 (~> 0.18)
      google-apis-storage_v1 (>= 0.42)
      google-cloud-core (~> 1.6)
      googleauth (~> 1.9)
      mini_mime (~> 1.0)
    google-cloud-trace (0.45.0)
      concurrent-ruby (~> 1.1)
      google-cloud-core (~> 1.5)
      google-cloud-trace-v1 (>= 0.0, < 2.a)
      google-cloud-trace-v2 (>= 0.0, < 2.a)
      stackdriver-core (~> 1.3)
    google-cloud-trace-v1 (0.9.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-trace-v2 (0.9.0)
      gapic-common (>= 0.21.1, < 2.a)
      google-cloud-errors (~> 1.0)
    google-logging-utils (0.2.0)
    google-protobuf (4.30.2-arm64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.30.2-x86_64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.30.2-x86_64-linux)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos (1.6.0)
      google-protobuf (>= 3.18, < 5.a)
      googleapis-common-protos-types (~> 1.7)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.18.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    grape (2.0.0)
      activesupport (>= 5)
      builder
      dry-types (>= 1.1)
      mustermann-grape (~> 1.0.0)
      rack (>= 1.3.0)
      rack-accept
    grape-active_model_serializers (1.5.2)
      active_model_serializers (>= 0.10.0)
      grape (>= 0.8.0)
    grape-entity (1.0.1)
      activesupport (>= 3.0.0)
      multi_json (>= 1.3.2)
    grape-swagger (2.1.2)
      grape (>= 1.7, < 3.0)
      rack-test (~> 2)
    grape-swagger-entity (0.6.2)
      grape-entity (~> 1)
      grape-swagger (~> 2)
    grape-swagger-rails (0.6.0)
      ostruct
      railties (>= 6.0.6.1)
    grape-swagger-representable (0.2.2)
      grape-swagger (>= 0.31.0)
      representable
    grape_devise_token_auth (0.1.4)
      devise (>= 3.3)
      devise_token_auth (>= 0.1.32)
      grape (> 0.9.0)
    grape_logging (1.8.4)
      grape
      rack
    grpc (1.70.1-arm64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.70.1-x86_64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.70.1-x86_64-linux)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    hashdiff (1.1.2)
    hashie (5.0.0)
    hashie-forbidden_attributes (0.1.1)
      hashie (>= 3.0)
    heapy (0.2.0)
      thor
    html_truncator (0.4.2)
      nokogiri (~> 1.5)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.9.0)
      mutex_m
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    i18n-timezones (1.4.8)
      i18n (>= 0.9.3)
      rails (>= *******)
    icalendar (2.11.0)
      base64
      ice_cube (~> 0.16)
      logger
      ostruct
    ice_cube (0.17.0)
    iconv (1.1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jquery-ui-rails (8.0.0)
      railties (>= 3.2.16)
    json (2.12.2)
    json-schema (5.1.1)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    jsonapi-renderer (0.2.2)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.0.0)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    lint_roller (1.1.0)
    liquid (5.8.6)
      bigdecimal
      strscan (>= 3.1.1)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memory_profiler (1.1.0)
    method_source (1.1.0)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0603)
    mini_histogram (0.3.1)
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    minitest (5.25.5)
    mobility (1.3.2)
      i18n (>= 0.6.10, < 2)
      request_store (~> 1.0)
    msgpack (1.7.5)
    multi_json (1.15.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mustermann (3.0.3)
      ruby2_keywords (~> 0.0.1)
    mustermann-grape (1.0.2)
      mustermann (>= 1.0.0)
    mutex_m (0.3.0)
    mysql2 (0.5.6)
    net-http (0.6.0)
      uri
    net-imap (0.5.6)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    netrc (0.11.0)
    newrelic_rpm (9.19.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    observer (0.1.2)
    octokit (7.2.0)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    offsite_payments (2.7.28)
      actionpack (>= 5.2.3)
      actionview (>= *******)
      active_utils (~> 3.3.0)
      activesupport (>= 5.2.3)
      builder (>= 2.1.2, < 4.0.0)
      i18n (>= 0.6.6)
      nokogiri (>= 1.8.5)
    oj (3.16.10)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.2.1)
      jwt (>= 2.9.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-okta (2.0.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.7, >= 1.7.1)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    omniauth-saml (2.2.4)
      omniauth (~> 2.1)
      ruby-saml (~> 1.18)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    package_json (0.1.0)
    paper_trail (15.2.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.27.0)
    parallel_tests (4.10.1)
      parallel
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pastel (0.8.0)
      tty-color (~> 0.5)
    pkg-config (1.5.9)
    playwright-ruby-client (1.52.0)
      concurrent-ruby (>= 1.1.6)
      mime-types (>= 3.0)
    poltergeist (1.18.1)
      capybara (>= 2.1, < 4)
      cliver (~> 0.3.1)
      websocket-driver (>= 0.2.0)
    pp (0.6.2)
      prettyprint
    pp_sql (2.1.0)
      activerecord
      anbt-sql-formatter (~> 0.1.0)
    premailer (1.23.0)
      addressable
      css_parser (>= 1.12.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.12.0)
      actionmailer (>= 3)
      net-smtp
      premailer (~> 1.7, >= 1.7.9)
    prettier_print (1.2.1)
    prettyprint (0.2.0)
    prism (1.4.0)
    process_executer (1.3.0)
    pronto (0.11.4)
      gitlab (>= 4.4.0, < 5.0)
      httparty (>= 0.13.7, < 1.0)
      octokit (>= 4.7.0, < 11.0)
      rainbow (>= 2.2, < 4.0)
      rexml (>= 3.2.5, < 4.0)
      rugged (>= 0.23.0, < 2.0)
      thor (>= 0.20.3, < 2.0)
    pronto-rubocop (0.11.6)
      pronto (~> 0.11.0)
      rubocop (>= 0.63.1, < 2.0)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.11.0)
      byebug (~> 12.0)
      pry (>= 0.13, < 0.16)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-accept (0.4.5)
      rack (>= 0.4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-proxy (0.7.7)
      rack
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-reverse-proxy (0.13.0)
      actionpack
      addressable
    rails_best_practices (1.23.2)
      activesupport
      code_analyzer (~> 0.5.5)
      erubis
      i18n
      json
      require_all (~> 3.0)
      ruby-progressbar
    rails_stats (1.0.2)
      rake
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rake-compiler-dock (1.9.1)
    randexp (0.1.7)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rb-readline (0.5.5)
    rb_sys (0.9.115)
      rake-compiler-dock (= 1.9.1)
    rbnacl (7.1.2)
      ffi (~> 1)
    rbnacl-libsodium (1.0.16)
      rbnacl (>= 3.0.1)
    rchardet (1.9.0)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    react-rails (3.2.1)
      babel-transpiler (>= 0.7.0)
      connection_pool
      execjs
      railties (>= 3.2)
      tilt
    recaptcha (5.19.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-activesupport (5.3.0)
      activesupport (>= 3, < 8)
      redis-store (>= 1.3, < 2)
    redis-client (0.23.2)
      connection_pool
    redis-store (1.10.0)
      redis (>= 4, < 6)
    regexp_parser (2.10.0)
    relation_to_struct (1.10.0)
      activerecord (>= 7.0, < 8.1)
      activesupport (>= 7.0, < 8.1)
    reline (0.6.1)
      io-console (~> 0.5)
    repost (0.4.2)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    require_all (3.0.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.4.1)
    rmagick (6.1.1)
      observer (~> 0.1)
      pkg-config (~> 1.4)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    rotp (6.3.0)
    rouge (4.2.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-activemodel-mocks (1.2.1)
      activemodel (>= 3.0)
      activesupport (>= 3.0)
      rspec-mocks (>= 2.99, < 4.0)
    rspec-benchmark (0.6.0)
      benchmark-malloc (~> 0.2)
      benchmark-perf (~> 0.6)
      benchmark-trend (~> 0.4)
      rspec (>= 3.0)
    rspec-collection_matchers (1.2.1)
      rspec-expectations (>= 2.99.0.beta1)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-its (2.0.0)
      rspec-core (>= 3.13.0)
      rspec-expectations (>= 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rubocop (1.77.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-capybara (2.22.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rake (0.7.1)
      lint_roller (~> 1.1)
      rubocop (>= 1.72.1)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rspec_rails (2.31.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
      rubocop-rspec (~> 3.5)
    ruby-ole (********)
    ruby-prof (1.7.2)
      base64
    ruby-progressbar (1.13.0)
    ruby-saml (1.18.0)
      nokogiri (>= 1.13.10)
      rexml
    ruby-statistics (4.1.0)
    ruby-vips (2.2.1)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    rubyXL (3.4.33)
      nokogiri (>= 1.10.8)
      rubyzip (>= 1.3.0)
    ruby_llm (1.3.1)
      base64
      event_stream_parser (~> 1)
      faraday (>= 1.10.0)
      faraday-multipart (>= 1)
      faraday-net_http (>= 1)
      faraday-retry (>= 1)
      marcel (~> 1.0)
      zeitwerk (~> 2)
    ruby_parser (3.21.0)
      racc (~> 1.5)
      sexp_processor (~> 4.16)
    rubyzip (2.4.1)
    rugged (1.7.2)
    saharspec (0.0.10)
      ruby2_keywords
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    searchkick (4.6.3)
      activemodel (>= 5)
      elasticsearch (>= 6, < 7.14)
      hashie
    secure_headers (6.7.0)
    securerandom (0.4.1)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    semantic_range (3.1.0)
    sexp_processor (4.17.1)
    shakapacker (8.3.0)
      activesupport (>= 5.2)
      package_json
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    shopify-money (3.2.0)
    should_not (1.1.0)
    shoulda-callback-matchers (1.1.4)
      activesupport (>= 3)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    signet (0.20.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_form (5.3.1)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    simpleidn (0.2.3)
    skylight (6.0.4)
      activesupport (>= 5.2.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    socket.io-rails (2.3.0)
      railties (>= 3.1)
    spreadsheet (1.3.1)
      bigdecimal
      ruby-ole
    sprockets (3.7.5)
      base64
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-helpers (1.4.0)
      sprockets (>= 2.2)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    ssrf_filter (1.1.2)
    stackdriver-core (1.7.0)
      google-cloud-core (~> 1.2)
    stackprof (0.2.27)
    stringio (3.1.7)
    strings (0.2.1)
      strings-ansi (~> 0.2)
      unicode-display_width (>= 1.5, < 3.0)
      unicode_utils (~> 1.4)
    strings-ansi (0.2.0)
    strscan (3.1.5)
    syntax_tree (6.2.0)
      prettier_print (>= 1.2.0)
    sys-memory (0.2.0)
      ffi (~> 1.1)
    temping (4.3.0)
      activerecord (>= 6.0, < 8.1)
      activesupport (>= 6.0, < 8.1)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    terrapin (1.0.1)
      climate_control
    terser (1.2.5)
      execjs (>= 0.3.0, < 3)
    test-prof (1.4.4)
    thor (1.3.2)
    thread (0.2.2)
    thread_safe (0.3.6)
    tilt (2.3.0)
    timecop (0.9.10)
    timeout (0.4.3)
    tolk (6.0.0)
      rails (>= 7.0, < 7.3)
      sprockets-rails (~> 3.4)
    trailblazer-option (0.1.2)
    tty-color (0.6.0)
    tty-screen (0.8.2)
    tty-table (0.12.0)
      pastel (~> 0.8)
      strings (~> 0.2.0)
      tty-screen (~> 0.8)
    turbo-rails (2.0.13)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    two_factor_authentication (2.2.0)
      devise
      encryptor
      rails (>= 3.1.1)
      randexp
      rotp (>= 4.0.0)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    uk_postcode (2.1.8)
    unaccent (0.4.0)
    unicode-display_width (2.6.0)
    unicode_utils (1.4.0)
    uniform_notifier (1.16.0)
    unparser (0.8.0)
      diff-lcs (~> 1.6)
      parser (>= 3.3.0)
      prism (>= 1.4)
    uri (1.0.3)
    useragent (0.16.11)
    vcr (6.3.1)
      base64
    version_gem (1.1.4)
    view_component (3.23.2)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1)
      method_source (~> 1.0)
    view_component-storybook (1.0.0)
      view_component (>= 2.54)
      yard (~> 0.9.25)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket (1.2.10)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    where_exists (3.0.0)
      activerecord (>= 5.2, < 8.1)
    wicked_pdf (2.8.2)
      activesupport
      ostruct
    xpath (3.2.0)
      nokogiri (~> 1.8)
    ya2yaml (0.31)
    yard (0.9.36)
    zeitwerk (2.7.3)

GEM
  remote: https://rubygems.pkg.github.com/marketdojo/
  specs:
    md_exts (0.5.5)
      rake (~> 13.0)
      rb_sys (~> 0.9.39)
      rspec (~> 3.0)

PLATFORMS
  arm64-darwin-20
  arm64-darwin-22
  arm64-darwin-23
  arm64-darwin-24
  x86_64-darwin-22
  x86_64-darwin-24
  x86_64-linux

DEPENDENCIES
  ZCRMSDK!
  aasm
  active_record_query_trace
  active_record_union (~> 1.3)
  activemerchant
  activemodel-serializers-xml
  activerecord-import
  activerecord-session_store
  acts-as-taggable-on!
  acts-as-tree-with-dotted-ids!
  acts_as_list
  acts_as_tree
  anbt-sql-formatter (~> 0.1.2)
  annotaterb
  api-pagination
  app_profiler
  authtrail
  benchmark-ips
  better_errors
  binding_of_caller
  bootsnap
  bootstrap-editable-rails
  bourbon
  braintree
  brakeman
  browser
  bullet
  cancancan
  capybara
  capybara-screenshot
  capybara-select2
  carrierwave
  category_dojo!
  caxlsx
  caxlsx_rails
  ckeditor (~> 4.3.0)
  coffee-script
  countries
  daemons
  dalli
  dead_end
  debug (>= 1.0.0)
  declarative_authorization!
  deep_cloneable
  delayed_job_active_record
  delocalize
  dentaku
  derailed_benchmarks
  devise
  devise-encryptable
  devise_token_auth (>= 1.2.0)!
  dynamic_form!
  easy_translate
  email_address
  exception_notification
  execjs
  factory_bot_instruments
  factory_bot_rails
  faker
  faraday
  faraday-httpclient
  faraday-retry
  fast_blank
  fast_jsonapi
  fasterer
  flamegraph
  fog-core (= 2.2.4)
  fog-google (~> 1.22)
  fog-json
  fog-xml
  gemini-ai
  gibberish
  git
  globalize
  globalize-versioning!
  google-cloud-error_reporting
  google-cloud-logging
  google-cloud-monitoring
  google-cloud-storage
  google-cloud-trace
  googleauth
  grape (= 2.0.0)
  grape-active_model_serializers
  grape-entity
  grape-swagger
  grape-swagger-entity
  grape-swagger-rails
  grape-swagger-representable
  grape_devise_token_auth
  grape_logging
  hashie-forbidden_attributes
  henkei!
  html_truncator
  i18n-timezones
  icalendar
  iconv
  jquery-ui-rails
  json
  json-schema
  jwt
  kaminari
  letter_opener
  liquid
  listen
  llm!
  md_exts (= 0.5.5)!
  memoist!
  memory_profiler
  mini_magick
  mobility (~> 1.3.0.rc3)
  mysql2
  nested_form!
  net-http
  newrelic_rpm
  nokogiri
  offsite_payments
  oj
  omniauth-azure-oauth2!
  omniauth-google-oauth2
  omniauth-okta (>= 2.0.0)
  omniauth-rails_csrf_protection
  omniauth-saml
  paper_trail (~> 15)
  parallel
  parallel_tests
  paranoia
  playwright-ruby-client (~> 1.51)
  poltergeist
  pp_sql (~> 2.1)
  premailer-rails
  pronto
  pronto-rubocop
  pry
  pry-byebug
  psych
  puma
  questionnaire_engine!
  rack-cors
  rack-mini-profiler
  rails (>= 7.2)
  rails-controller-testing
  rails-reverse-proxy
  rails_best_practices (>= 1.19)
  rails_stats
  rainbow
  rake
  rb-readline
  rbnacl
  rbnacl-libsodium
  react-rails
  recaptcha
  redis
  redis-activesupport
  relation_to_struct
  remotipart!
  repost
  rmagick
  roo
  roo-xls
  rspec-activemodel-mocks
  rspec-benchmark
  rspec-collection_matchers
  rspec-its
  rspec-mocks
  rspec-rails
  rubocop (>= 1.0.0)
  rubocop-ast
  rubocop-capybara
  rubocop-factory_bot
  rubocop-performance
  rubocop-rails
  rubocop-rake
  rubocop-rspec (>= 2.0.0.pre)
  rubocop-rspec_rails
  ruby-prof
  rubyXL
  ruby_llm
  rubyzip
  saharspec
  sassc-rails
  searchkick (< 5)
  secure_headers
  shakapacker (~> 8)
  shopify-money
  should_not
  shoulda-callback-matchers
  shoulda-matchers (~> 6.2)
  sim_dojo!
  simple_enum!
  simple_form
  simplecov
  skylight
  socket.io-rails (~> 2.3.0)
  sprockets (< 4)
  sprockets-helpers
  sprockets-rails
  srm!
  stackprof
  syntax_tree
  sys-memory
  temping
  terminal-table
  terser
  test-prof
  timecop
  tolk
  tty-table (~> 0.12.0)
  turbo-rails
  two_factor_authentication
  uk_postcode
  unparser (~> 0.8.0)
  vcr
  view_component
  view_component-storybook (~> 1.0)
  web-console (~> 4.2)
  webdrivers
  webmock
  where_exists (~> 3.0)
  wicked_pdf
  ya2yaml

RUBY VERSION
   ruby 3.2.2p53

BUNDLED WITH
   2.5.16
