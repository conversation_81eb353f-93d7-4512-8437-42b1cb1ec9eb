# frozen_string_literal: true

# ContractController
class ContractsController < ApplicationController
  include FeatureHelper
  include CustomFieldValuesControllerMethods
  protect_from_forgery with: :exception, prepend: true,
                       except: %i[record_not_found upload]
  before_action :set_permitted_contract_ids, only: %w[index]
  before_action :load_custom_columns_dashboard
  before_action :set_contract, only: %w[edit update mark_archive
                                        add_contact add_stakeholder remove_user
                                        add_file remove_file add_notes show
                                        add_label update_note
                                        remove_relation add_relation
                                        add_category create_update_custom_column
                                        create_update_custom_column_values
                                        add_or_update_contract_notification
                                        download_file_document
                                        audit_trail]
  before_action :check_access_privilege
  before_action :remove_contract_from_session, only: %w[index show edit]
  before_action :set_cache_headers, only: :index
  filter_resource_access attribute_check: true,
                         collection: %w[index upload download
                                        change_contracts_owner
                                        filter_contracts download_template],
                         strong_parameters: true
  layout 'contract_layout'

  def index
    mark_interest
    load_index_data
  end

  def filter_contracts
    selected_contract_ids = load_contracts_for_index
    filter_params = Contract::Search::Filter.new(controller_params: params).call
    filtered = filter_params.present? || params[:search].present?
    respond_to do |format|
      format.json do
        render json: {
          selected_contract_ids:, filtered:
        }
      end
    end
  end

  def show
    if permitted_to?(:edit, @contract) && @contract.current_state != 'archived'
      redirect_to edit_contract_path(@contract)
      nil
    end
    load_associated_data
  end

  def new
    load_conditional_contract
  end

  def edit
    load_associated_data
    if @contract.current_state == 'archived' ||
       (!permitted_to?(:edit, @contract) &&
       permitted_to?(:show, @contract))
      redirect_to contract_path(@contract)
      nil
    end
  end

  def update
    if params[:save_contract]
      save_contract
    elsif @contract.update(contract_params)
      update_contract_notification
      update_single_contract_field
    else
      respond_to do |format|
        format.js do
          render json: @contract.errors, status: :unprocessable_entity
        end
      end
    end
  end

  def mark_archive
    msg, error, status = destroy_or_archive
    respond_to do |format|
      format.json do
        render json: {
          contract_archived_destroy_status: msg,
          error:,
          status:,
          new_state: @new_state
        }
      end
    end
  end

  def add_contact
    @error = []
    @added_users = []
    contract_users = @contract.contact_users.ids
    add_user(associated_params[:contact_user], contract_users, true)
    respond_to do |format|
      format.js do
        if @error.any?
          render json: load_user_data.merge!(error: [@error]), status: :ok
        else
          render json: load_user_data, status: :ok
        end
      end
    end
  end

  def add_stakeholder
    @error = []
    @added_users = []
    stake_holders = @contract.stakeholder_users.ids
    add_user(associated_params[:stakeholder], stake_holders, false)
    respond_to do |format|
      format.js do
        if @error.present?
          render json: load_user_data.merge!(error: [@error]), status: :ok
        else
          render json: load_user_data, status: :ok
        end
      end
    end
  end

  def remove_user
    @contract_user = ContractUser.find_by(contract_id: @contract.id,
                                          user_id: params[:user_id])
    if @contract_user.user_type == :contact &&
       @contract.contact_users.count <= 1
      @error = I18n.t('contract_errors.errors.cant_remove_contract_user')
    elsif @contract_user.destroy
      @contract.check_and_update_position(@contract_user)
      @contract.remove_notification_recipients(@contract_user)
    end
    @contract.reindex
    respond_to do |format|
      format.js do
        if @error.present?
          render json: load_user_data.merge!(error: [@error]), status: :ok
        elsif @contract_user.errors.any?
          render json: load_user_data
            .merge!(error: [@contract_user.errors]), status: :ok
        else
          render json: load_user_data, status: :ok
        end
      end
    end
  end

  def add_file
    file_type = @contract.company.contract_document_types.find(params[:file_type])
    @versioned_file = VersionedFile.new(
      name: @contract.title, user: @contract.user,
      company_id: current_user.company_id, file_type: file_type.name
    )
    respond_to do |format|
      format.js do
        if @versioned_file.save
          @contract.files << @versioned_file
          @document = save_documents(
            @versioned_file, document_params[:filename]
          )
          @document.user_id = current_user.id
          if @document.save
            @contract.reindex
            render json: { files: load_file_data }, status: :ok
          else
            @versioned_file.destroy
            render json: @document.errors, status: :unprocessable_entity
          end
        else
          render json: @versioned_file.errors, status: :unprocessable_entity
        end
      end
    end
  end

  def remove_file
    @file = VersionedFile.find(params[:file_id])
    @contract.contract_files.find_by(versioned_file_id: @file.id).destroy
    respond_to do |format|
      format.js do
        if @file.destroy
          @contract.reindex
          render json: { files: load_file_data }, status: :ok
        else
          render json: @file.errors, status: :unprocessable_entity
        end
      end
    end
  end

  def add_notes
    @note = @contract.contract_notes.new(note_params)
    @note.body = note_params[:body]
    if @note.body?
      @note.user_id = current_user.id
      @note.save
    else
      @error = I18n.t('contract_errors.errors.cant_create_empty_note')
    end
    @contract.reindex
    respond_to do |format|
      format.js do
        if @error.present?
          render json: [@error], status: :unprocessable_entity
        else
          render json: { notes: load_notes_data }, status: :ok
        end
      end
    end
  end

  def update_note
    @note = ContractNote.find_by(id: params[:note_id])
    respond_to do |format|
      format.js do
        if @note.update(note_params)
          render json: { notes: load_notes_data }, status: :ok
        else
          render json: [@note.errors], status: :unprocessable_entity
        end
      end
    end
  end

  def sort
    params[:user].each.with_index(1) do |user, index|
      case params[:type]
      when 'contact'
        @contract.contacts.where(user_id: user).update_all(position: index)
      when 'stakeholder'
        @contract.stakeholders.where(user_id: user).update_all(position: index)
      end
    end
  end

  def download_file_document
    file = @contract.files&.find(params[:file_id])
    document = (file.documents.find(params[:document_id]) if file&.documents&.present?) # rubocop:disable Lint/RedundantSafeNavigation
    @data = document.fetch_document_from_storage if document
    if @data
      respond_to do |format|
        format.html do
          send_data @data, disposition: document.disposition,
                           filename: document.display_name,
                           buffer_size: 200
        end
      end
    else
      return_alert_on_missing_file(document)
    end
  end

  def download_template
    zip = Contract::Download::Package.new(
      contracts: Contract.generate_template_contract,
      recipient: current_user,
      type: 'template'
    ).call
    respond_to do |format|
      format.html do
        send_zip_file(zip)
      end
    end
  end

  def download
    @download_contracts = Contract.where(id: download_ids.split(','))
    DownloadContractsJob.perform_later(
      contracts: @download_contracts.to_a,
      recipient: current_user,
      type: params[:type]
    )

    flash[:notice] = I18n.t('contract_download_prepared')
    redirect_to contracts_path
  end

  def add_label
    @contract_user = ContractUser.find_by(
      contract_id: params[:id], user_id: params[:contract_user][:user_id]
    )
    @contract_user.update(contract_user_params)
    @contract_user.user.update(user_params)
    @contract.reindex
    respond_to do |format|
      format.js do
        if @contract_user.errors.present?
          render json: @contract_user.errors, status: :unprocessable_entity
        else
          render json: {
            @contract_user.id => contract_user_params[:label]
          }, status: :ok
        end
      end
    end
  end

  def add_relation
    parent_id = params[:parent_id]
    child_id = params[:child_id]
    if parent_id.present?
      other_contract = @contract
      other_contract.parent_id = parent_id
    elsif child_id.present?
      other_contract = Contract.find(child_id)
      other_contract.parent_id = params[:id]
    end
    respond_to do |format|
      format.js do
        if other_contract.save
          render json: load_parent_child_data, status: :ok
        else
          render json: other_contract.errors, status: :unprocessable_entity
        end
      end
    end
  end

  def remove_relation
    other_contract = Contract.find(params[:other_contract_id])
    other_contract.parent_id = nil
    respond_to do |format|
      format.js do
        if other_contract.save
          @contract.reload if other_contract == @contract
          render json: load_parent_child_data, status: :ok
        else
          render json: other_contract.errors, status: :unprocessable_entity
        end
      end
    end
  end

  def add_category
    associate_category(params[:contract][:contract_category]) if params.dig(:contract, :contract_category)
    respond_to do |format|
      format.js do
        render json: load_category_data, status: :ok
      end
    end
  end

  def add_or_update_contract_notification
    process_contract_notification if params.dig(:contract_notification, :interval).present?
    respond_to do |format|
      format.js do
        render json: load_data_after_update('interval'), status: :ok
      end
    end
  end

  def change_contracts_owner
    contract_ids = params[:contract_ids]
    target_owner_id = params[:target_owner_id].to_i
    contracts = Contract.includes(:user).where(id: contract_ids).each do |contract|
      next if contract.user_id == target_owner_id

      # Need to update user id as target owner id
      # on owner change
      contract.update_attribute('user_id', target_owner_id)
      contract.remove_target_owner_as_stakeholder
      contract.remove_target_owners_contract_permission
    end
    respond_to do |format|
      format.json do
        render json: {
          contracts: Contract::CustomAttributes.call(contracts),
          message: I18n.t('contract_owner_changed'),
          status: :ok
        }
      end
    end
  end

  def audit_trail
    render json: { history: load_history }, status: :ok
  end

  private

  def set_cache_headers
    response.headers['Cache-Control'] = 'no-cache, no-store, max-age=0, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
  end

  def load_custom_columns_dashboard
    @custom_columns_dashboard = current_user.company.contract_custom_column_item_types_to_show_in_summary.uniq
  end

  def load_parent_stakeholders
    parents = @contracts.filter_map { |contract| contract['parent_id'] }
    @parent_stakeholders = ContractUser.where(
      contract_id: parents,
      user_type_cd: 1
    ).pluck(:contract_id, :user_id)

    grouped_stakeholders = @parent_stakeholders.group_by { |contract_id, _| contract_id }

    parents.each_with_object({}) do |pid, ps|
      stakeholder_ids = grouped_stakeholders[pid]&.map { |_, user_id| user_id } || []
      ps[pid] = { stakeholders: stakeholder_ids }
    end

    @parent_stakeholders
  end

  def load_contract_company_names(contract_ids)
    contact_company_name = current_contact_company_names(contract_ids)
    @contact_company_names_array = contact_company_name.map do |ccn|
      { 'contact_company_name' => ccn }
    end
  end

  def current_contact_company_names(contract_ids)
    ContractUser.where(contract_id: contract_ids).pluck(:contact_company_name)
                .compact.uniq
  end

  def load_index_data
    load_contracts_for_index
    load_filterable_data
    load_hosts_with_contract_show_permission
    @sim_event = current_user.company.sim_dojo_event
    @contracts = current_user.contracts_for_user(@sim_event&.id)
    load_custom_column_data_for_index(@contracts)
    load_parent_stakeholders
  end

  def load_custom_column_data_for_index(contracts)
    @options = helpers.get_custom_column_values_hash_helper(@custom_columns_dashboard, contracts)
  end

  def add_user(emails, users, is_participant)
    emails.split(',').each do |email|
      if email&.match?(Devise.email_regexp)
        user = User.new_if_required_for_contract(email, current_user,
                                                 is_participant)
        check_user_validation(users, user, is_participant)
      else
        @error << I18n.t('contract_errors.errors.invalid_email', email:)
      end
    end
    @contract.reindex
  end

  def check_user_validation(users, user, is_participant)
    if user.valid?
      if @contract.contract_owner?(user)
        @error << I18n.t(
          'contract_errors.errors.cant_add_contract_owner',
          email: user.email
        )
      end
      role = is_participant ? 'Participant' : 'Host'
      check_and_associate_user(users, user, role)
    else
      @error << I18n.t(
        'contract_errors.errors.invalid_email', email: user.email
      )
    end
  end

  def remove_contract_from_session
    session[:saved_contract] = nil
  end

  def load_contracts_for_index
    contracts = contracts_by_filter
    contract_ids = contracts.ids
    load_contract_company_names(contract_ids)
    @custom_column_values_hash = {}
    Contract.custom_column_values_and_stakeholders_hash(contracts, contract_ids, current_user,
                                                        @custom_column_values_hash)
    contracts.ids
  end

  def contracts_by_filter
    seeker = Contract::Search::Seek.new(
      user: current_user,
      query: params[:search] || '*',
      filters: Contract::Search::Filter.new(controller_params: params),
      sort_order: Contract::Search::SortOrder.new(order: sort_params)
    )
    seeker.all
  end

  def set_contract
    contract_id = params[:contract_id]
    @contract = if contract_id.present?
                  Contract.find(contract_id)
                else
                  Contract.find(params[:id])
                end
  end

  def update_contract_state
    if @contract.pending?
      @contract.set_pending!
    elsif @contract.expiring?
      @contract.set_expiring!
    elsif @contract.expired?
      @contract.set_expired!
    else
      @contract.set_active!
    end
  end

  def associate_category(category_id)
    if category_id.blank?
      old_category = @contract.contract_category
      old_category.destroy if old_category.present?
    else
      category = CategoryDojo::Category.find(category_id)
      contract_category = @contract.category
      evaluate_contract_categories(contract_category, category)
    end
    @contract.reindex
  end

  def evaluate_contract_categories(contract_category, category)
    return if category.blank?

    if contract_category.exists?(category.id)
      contract_category.destroy(category.id)
    else
      contract_category << category
    end
  end

  def process_contract_notification
    if params[:contract] && params[:contract][:notice_period]
      params[:contract_notification] = {
        start_date: params[:contract][:notice_period]
      }
    end
    if contract_notification_params &&
       contract_notification_params[:interval]
      notification_hash = contract_notification_params['interval'].to_h
      contract_notification_params[:interval] =
        { notification_hash.keys[0].to_i => notification_hash.values[0] }
    end

    return if contract_notification_params.blank?

    contract_notification = @contract.default_contract_notification
    @contract_notification = contract_notification ||
                             @contract.contract_notifications.new
    @contract_notification.update(contract_notification_params)
  end

  def user_errors(user_type, user)
    I18n.t('contract_errors.errors.user_already_added', email: user.email,
                                                        user_type:)
  end

  def check_and_associate_user(users, user, role)
    user_id = user.id
    @error << user_errors('Contact', user) if user.persisted? && users.include?(user_id)

    @error << I18n.t("user_must_be_#{role.downcase}", email: user.email) if user.acting_as != role

    return @error if @error.any? { |error| error.include?(user.email) }

    if role == 'Participant'
      @contract.contract_users.create(user:, contact_company_name: user.user_company_name, user_type_cd: 0)
    else
      @contract.stakeholder_users << user
    end
    users << user_id
    @added_users << user
  end

  def load_filterable_data
    possible_contracts = load_possible_contracts

    return unless possible_contracts.size.positive?

    load_owners(possible_contracts)
    load_states(possible_contracts)
    load_titles(possible_contracts)
    load_categories(possible_contracts)
    load_suppliers(possible_contracts)
    load_references(possible_contracts)
    load_stakeholders(possible_contracts)
  end

  def load_suppliers(possible_contracts)
    @suppliers = possible_contracts
                 .joins(contract_users: :user)
                 .where.not(contract_users: { user_type_cd: 1 })
                 .pluck(Arel.sql('DISTINCT users.email'))
    @suppliers = @suppliers.compact.sort
  end

  def load_owners(possible_contracts)
    owner_emails = possible_contracts.joins(:user).pluck(Arel.sql('DISTINCT users.email'))
    @owners = (owner_emails | other_permitted_owners).compact.sort
  end

  def load_stakeholders(possible_contracts)
    @stakeholders = possible_contracts
                    .joins(stakeholder_users: :contract_users)
                    .where(contract_users: { deleted_at: nil, user_type_cd: 1 })
                    .pluck(Arel.sql('DISTINCT users.email'))
    @stakeholders = @stakeholders.compact.sort
  end

  def load_states(possible_contracts)
    @states = possible_contracts.distinct.pluck(:current_state)
    @states = @states.compact.map { |state| [I18n.t(state.to_sym), state] }.sort_by { |pair| pair[1] }
  end

  def load_titles(possible_contracts)
    @titles = possible_contracts.distinct.pluck(:title).compact.sort
  end

  def load_categories(possible_contracts)
    @categories = possible_contracts.joins(:category).pluck(Arel.sql('DISTINCT category_dojo_categories.name'))
    @categories = @categories.compact.sort
  end

  def load_references(possible_contracts)
    @reference = possible_contracts.distinct.pluck(:contract_number).compact.sort
  end

  def load_possible_contracts
    if session[:archived] == 'true'
      Contract.includes(:user, :category, :contact_users, :stakeholder_users)
              .with_archived(current_user)
    else
      Contract.includes(:user, :category, :contact_users, :stakeholder_users)
              .unarchived(current_user)
    end
  end

  def other_permitted_owners
    permitted_owners = current_user.contract_permissions.where(resource_type: 'User').pluck(:resource_id)
    User.where(id: permitted_owners).pluck(:email) << current_user.email
  end

  def load_hosts_with_contract_show_permission
    return unless current_user.allow_contract_action(:edit)

    company = current_user.company
    @contract_hosts = company.hosts_with_contract_show_permission
  end

  def sort_params
    order = {}
    default = { created_at: :desc }
    return default unless params[:sort_by] && params[:sort_order]

    order[params.require(:sort_by).to_sym] = params.require(:sort_order).to_sym
    order
  end

  def contract_params
    params.require(:contract).permit(:title, :contract_number, :start_date,
                                     :expiry_date, :annual_value, :total_value,
                                     :notice_period, :auto_renew, :currency_id,
                                     :sequential_contract_id, :alert_date)
  end

  def associated_params
    return unless params[:associated_data]

    params.require(:associated_data)
          .permit(:category, :contact_user, :stakeholder)
  end

  def note_params
    params.require(:note_data).permit(
      :body,
      :private,
      :bookmark,
      :include_in_alerts
    )
  end

  def document_params
    return if params[:document].blank?

    params.require(:document).permit(filename: {})
  end

  def upload_params
    return if params[:contract_zip].blank? || params[:contract_zip] == 'null'

    params.require(:contract_zip)
  end

  def contract_notification_params
    return if params[:contract_notification].blank?

    params.require(:contract_notification).permit(:start_date, interval: {})
  end

  def download_ids
    return if params[:download_ids].blank?

    params.require(:download_ids)
  end

  def check_access_privilege
    return if @contract.present? && @contract.user == current_user

    permission_denied unless has_permission?
    permission = Contract::Permission.new(current_user, params[:action])
    @view_only = permission.view_only?
    @can_create_contract = current_user.allow_admin_section(:contracts, :edit)
  end

  # This is to fetch all the permitted contract according to permission.
  # On view we need this information to decide URL for redirection
  # If we don't do it here then we have to do this for every contract on view
  def set_permitted_contract_ids
    @show_contracts = current_user.rule_for_show_contracts
    @edit_contracts = current_user.rule_for_edit_contracts
  end

  # we are using admin_permissions for listing & creating new contract and
  # contract_permissions for editing them
  def has_permission?
    permission = if %w[index new].include? params[:action]
                   Contract::Admin::Permission
                 else
                   Contract::Permission
                 end
    permission.new(current_user, params[:action]).can_access? &&
      !current_user.in_sandpit?
  end

  def destroy_or_archive
    case @contract.current_state
    when 'draft'
      if @contract.children.exists? # much more efficient than .present?, a little more than .any?
        msg = I18n.t(:contract_deletion_failed_text, title: @contract.title)
        error = [msg]
        @new_state = @contract.current_state
        status = :unprocessable_entity
      else
        @contract.destroy
        msg = I18n.t(:contract_deleted_text, title: @contract.title)
        @new_state = 'deleted'
      end
    when 'archived'
      unarchived_state
      msg = I18n.t(:contract_unarchived_text, title: @contract.title)
    else
      @contract.set_archived!
      msg = I18n.t(:contract_archived_text, title: @contract.title)
      @new_state = 'archived'
    end
    [msg, error || [], status || :ok]
  end

  def unarchived_state
    if @contract.expiry_date < Time.zone.today
      @contract.update(current_state: 'expired')
    elsif @contract.expiry_date == Time.zone.today
      @contract.update(current_state: 'expiring')
    else
      version_contract = @contract.version_object.last
      previous_state = YAML.safe_load(version_contract.object_changes, aliases: true)['current_state'][0]
      @contract.update(current_state: previous_state)
    end
    @new_state = @contract.current_state
  end

  def contract_user_params
    params.require(:contract_user).permit(:label, :contact_company_name)
  end

  def user_params
    params.require(:user).permit(:name, :office_phone)
  end

  def mark_interest
    current_user.activate_contracts(context: :visit)
  end

  def load_conditional_contract
    saved_contract = session[:saved_contract]
    if saved_contract.present?
      @contract = Contract.find(saved_contract)
      load_data_and_history
    else
      @contract = Contract.create(user_id: current_user.id)
      if @contract.errors.present?
        flash[:alert] = @contract.errors.full_messages.last
        redirect_to contracts_path
      else
        session[:saved_contract] = @contract.id
        load_data_and_history
      end
    end
  end

  def load_data_and_history
    load_associated_data
  end

  def load_category_data
    data = Contract::AssociatedData.new(
      current_user, @contract.reload, @contract.company
    )
    data.category_data
  end

  def load_parent_child_data
    data = Contract::AssociatedData.new(
      current_user, @contract, @contract.company
    )
    data.parent_child_relation
  end

  def load_notes_data
    data = Contract::AssociatedData.new(
      current_user, @contract, @contract.company
    )
    data.notes
  end

  def load_user_data
    data = Contract::AssociatedData.new(
      current_user, @contract, @contract.company
    )
    data.contract_user_data
  end

  def load_file_data
    data = Contract::AssociatedData.new(
      current_user, @contract, @contract.company
    )
    data.file_data
  end

  def load_data_after_update(action)
    data = Contract::AssociatedData.new(
      current_user, @contract, @contract.company
    )
    if %w[title contract_number sequential_contract_id].include?(action)
      data.basic_details
    elsif %w[annual_value total_value currency_id].include?(action)
      data.contract_values
    elsif %w[start_date expiry_date notice_period auto_renew
             interval].include?(action)
      data.dates_and_alert_data
    end
  end

  def load_associated_data
    data = Contract::AssociatedData.new(
      current_user, @contract, @contract.company
    )
    @basic_details = data.basic_details
    @parent_child_relation = data.parent_child_relation
    @category_relation = data.category_data
    @custom_columns = data.custom_columns_data
    @contract_file = data.file_data
    @contract_values = data.contract_values
    @contract_user_data = data.contract_user_data
    @dates_and_alerts = data.dates_and_alert_data
    @custom_notifications = data.custom_notification_data
    @contract_notes = data.notes
    @document_file_types = data.document_file_types
  end

  def save_documents(versioned_file, params)
    return if params.blank?

    base64_image = params['source']
    string = base64_image.split(/[:;,]/).last
    file_name = document_name(params['name'])
    tempfile = Tempfile.new([file_name[0], file_name[1]])
    tempfile.binmode
    tempfile.write Base64.decode64(string)
    tempfile.rewind
    content_type = system('file --mime -b', tempfile.path.split(';')[0])
    document = versioned_file.documents.create(
      content_type:,
      filename: tempfile,
      name_of_document: file_name[0],
      user_id: current_user.id
    )
    tempfile.close
    tempfile.unlink
    document
  end

  def document_name(fname)
    file_name = fname.tr(' ', '_').split('.')
    name = file_name[0..-2].join('_')
    extension = ".#{file_name[-1]}"
    [name, extension]
  end

  def save_contract
    respond_to do |format|
      format.js do
        update_contract_state
        render json: @contract, status: :ok
      end
    end
  end

  def update_contract_notification
    process_contract_notification if params[:contract][:notice_period].present?
  end

  def update_single_contract_field
    return unless params[:single_field]

    respond_to do |format|
      format.js do
        response = load_data_after_update(contract_params.keys.last)
        render json: response, status: :ok
      end
    end
  end
end
