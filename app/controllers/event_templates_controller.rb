# frozen_string_literal: true

# Controller for Event Templates
class EventTemplatesController < ApplicationController
  include Ruleable
  include TemplateRulesExportable

  skip_before_action :set_sandpit_banner, only: [:create]
  before_action :set_params, only: %i[create use_template]
  before_action :set_company, if: -> { %i[index use_template update export_rules].include?(action_name.to_sym) }
  before_action :create_event_template_from_event, only: %i[create]
  before_action :rules_modal_disabled, only: %i[index update]

  filter_access_to(
    %i[create update destroy],
    attribute_check: true,
    nested_in: :companies
  )
  filter_access_to %i[index use_template admin_index set_global set_example create_from_autoselection export_rules]

  layout 'admin'

  def index
    search_query = params[:search] ? params[:search].strip : '%'
    @title = t('enums.admin_permission.section.event_library')
    @event_templates = @company
                       .event_templates
                       .where("name LIKE CONCAT('%', ?, '%')", search_query)
                       .order(sort_column.to_s => sort_direction.to_sym)
                       .page(params[:page])
                       .per(10)
    page = params[:page].to_i
    if @event_templates.empty? && page > 1
      page = (page - 1) > 1 ? page - 1 : 1
      redirect_to company_event_templates_path(@company, page:)
    else
      respond_to do |format|
        format.html { render 'index' }
        format.js   { render 'index' }
      end
    end
  end

  def admin_index
    get_global_local_and_example_templates
    @company = current_user.company
    respond_to do |format|
      format.html { render 'admin_index' }
    end
  end

  def set_global
    event_template = EventTemplate.find(params[:event_template])
    event_template.global!(params[:global])
    get_global_local_and_example_templates
    respond_to do |format|
      format.js { render 'set_global' }
    end
  end

  def set_example
    event_template = EventTemplate.find(params[:event_template])
    unset_example_if_exist if params[:example] == 'true'
    event_template.example!(params[:example])
    get_global_local_and_example_templates
    respond_to do |format|
      format.js { render 'set_example' }
    end
  end

  def use_template
    @event = EventTemplate.build_event_from_template(params, current_user, use_template_params)

    if @event.persisted?
      flash[:notice] = t(:template_copied_successfully)
      respond_to do |format|
        format.js { render 'use_template', locals: { example_event: params[:event] == 'example_event' } }
      end
    else
      render 'use_template_failed'
    end
  end

  def create_from_autoselection
    params['event_template'] = params['automated_event_template']
    @event = EventTemplate.build_event_from_template(params, current_user, use_template_params)

    if @event.custom_column_values.empty?
      # NOTE: this still creates ccv values for false checkboxes as we pass this in from the form
      @event.custom_column_values_attributes = params['custom_column_values_attributes'].permit!
      @event.save
    end

    if @event.persisted?
      flash[:notice] = t(:template_copied_successfully)
      respond_to do |format|
        format.js { render 'use_template', locals: { example_event: params[:event] == 'example_event' } }
      end
    else
      render 'use_template_failed'
    end
  end

  def create
    if @event_template.persisted?
      flash.now[:success] = I18n.t('questionnaire_library.template_saved')
    else
      flash.now[:error] = @event_template.errors.full_messages
    end
    respond_to do |format|
      format.js { render 'create' }
    end
  end

  def update
    @event_template.global!(params[:global]) if params[:global]
    @event_template.update(event_template_params)
    if @event_template.valid?
      flash.now[:success] = I18n.t('questionnaire_library.template_saved')
    else
      flash.now[:error] = @event_template.errors.full_messages
    end
    respond_to do |format|
      format.js { render 'update' }
    end
  end

  def destroy
    @event_template.destroy
    respond_to do |format|
      format.html { redirect_back fallback_location: '/users/home' }
    end
  end

  private

  def rules_modal_disabled
    @rules_modal_disabled = check_rules_disabled(@current_user.company, Event.name)
  end

  def get_global_local_and_example_templates
    @local_templates_for_global = EventTemplate.local_templates_for_global.order(:name)
    @local_templates_for_example = EventTemplate.local_templates_for_example.order(:name)
    @global_templates = EventTemplate.global_templates.order(:name)
    @example_templates = EventTemplate.example_templates.order(:name)
  end

  def unset_example_if_exist
    example_ids = TemplateDetail.where(example: true).ids
    TemplateDetail.where(id: example_ids).update_all(example: false) if example_ids.present?
  end

  # Did this for decl authorization so that it passes the attribute check
  def create_event_template_from_event
    event = Event.find(params[:event_id])
    if permitted_to? :edit, event
      @event_template = EventTemplateService::Create.new(
        event:, user: current_user
      ).call
    else
      permission_denied
    end
  end

  def use_template_params
    params.permit(
      :from_sandpit,
      *Event::CLONE_OPTIONS
    )
  end

  def event_template_params
    return if params[:event_template].blank?

    params.require(:event_template).permit(:name)
  end

  def set_company
    @company = Company.find(params[:company_id])
  end

  def sortable_columns
    %w[name created_at]
  end

  # Creating a template from an event or vice versa is all-or-nothing
  # So we set all the optional params to true
  def set_params
    clone_params = Event::CLONE_OPTIONS.index_with { 'true' }.with_indifferent_access
    params.merge(clone_params)
  end

  def set_from_sandpit(params)
    if current_user.in_sandpit?
      params[:from_sandpit] == 'true' ? 0 : 1
    else
      params[:from_sandpit] == 'true' ? 1 : 0
    end
  end
end
