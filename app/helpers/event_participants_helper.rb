# frozen_string_literal: true

require 'skylight'

# event participant helper
module EventParticipantsHelper
  extend Memoist
  include Skylight::Helpers

  # Render HTML to represent the approval status for each event
  # Shown to participants
  def participant_approval_status_html(e_par)
    # Options should include ep (event_participant)
    ep_status_symbol = e_par.acceptance_status_symbol
    ep_status = I18n.t(ep_status_symbol)

    div_class = ['check-boll']
    icon_class = ['fa']

    if %i[event_participant_pending_your_acceptance
          event_participant_pending_host_acceptance
          event_participant_invited
          invitation_expired].include?(ep_status_symbol)
      button_content = '...'
      div_class << ' rose-bg orange-color'
      icon_class << ' fa-ellipsis-h'
    elsif %i[event_participant_accepted
             event_completed].include?(ep_status_symbol)
      button_content = t(:yes_html)
      icon_class << ' fa-check'
    elsif %i[event_participant_declined
             event_participant_rejected_by_host
             event_participant_not_approved].include?(ep_status_symbol)
      button_content = "#{t(:no_html)} "
      div_class << ' rose-bg red-color'
      icon_class << ' fa-times'
    end

    tag.div(button_content, class: div_class.join) do
      tag.i('', class: icon_class.join, title: ep_status)
    end
  end

  # Render HTML to represent the approval status for each participant
  # Shown to hosts

  def host_acceptance_status_html(event, e_par, permitted_to_accept = false)
    r = ''
    ep_event = e_par.event
    ep_status_symbol = e_par.host_acceptance_status_symbol
    ep_status = I18n.t(ep_status_symbol)
    dropdown = false
    # Setup the dropdown if its needed
    event_current_state = ep_event.current_state
    if (%w[pending rfq post_rfq_pre_auction running pause]
            .include?(event_current_state) ||
           (ep_event.rfq? && event_current_state == 'completed')) &&
       !ep_event.event_passed_final_deadline_at_running? && permitted_to_accept
      dropdown = true
    end
    if dropdown
      r = []
      r << "<ul class='dropdown-menu'>"
      if %i[Declined event_participant_not_approved].include?(ep_status_symbol)
        r << "<li>#{raw link_to t(:accept), accept_event_event_participant_path(event, e_par.id),
                                remote: true,
                                confirm: t(:host_accepted_participant_after_declined), method: :put}</li>"
      end
      if ['event_participants.show.accepted_html', :event_participant_not_approved].include?(ep_status_symbol)
        r << "<li>#{raw link_to t(:reject), rejection_note_event_event_participant_path(event, e_par),
                                remote: true}</li>"

      end

      r << '</ul>'
      r = r.join.html_safe
    end
    button_class = ['btn-group-sm']
    button_class << ' dropdown' if dropdown
    if %i[event_participant_not_approved
          invitation_expired].include?(ep_status_symbol)
      button_content = '...'
      button_id = 'accept_decline_links_box'
      button_class << ' button-orange'
      ep_status_icon = I18n.t('list_symbol')
    elsif [:Declined].include?(ep_status_symbol)
      button_content = t(:no_html) + '&nbsp;'.html_safe
      button_id = 'accept_links_box'
      button_class << ' button-no'
      ep_status_icon = I18n.t('declined_symbol')
    elsif ['event_participants.show.accepted_html'].include?(ep_status_symbol)
      button_content = t(:yes_html)
      button_id = 'accept_decline_links_box'
      button_class << ' button-yes'
      ep_status_icon = I18n.t('accepted_symbol')
    end

    tag.div(button_content, class: button_class.join, id: button_id) do
      tag.a(button_content, class: 'button-label btn btn-default dropdown-toggle', title: ep_status, href: '#',
                            data: { toggle: 'dropdown' }) do
        ep_status_icon.html_safe
      end + r
    end
  end

  # Render HTML to represent the onboarding status for each participant
  # Shown to hosts
  # Dont show anything if onboard, or company doesnt do onboarding
  def onboarding_status_html(event_participant)
    status = event_participant.onboarding_status[0]
    return if status.nil? || status == :onboard

    case status
    when :not_onboarded
      outer_class = 'red-color'
      inner_class = 'fa fa-exclamation-triangle '
      text = t('event_participants.show.onboarding_status.not_onboarded')
      status_html(outer_class, inner_class, text)
    when :expiring
      outer_class = 'maybe'
      tooltip =
        t('event_participants.show.onboarding_status.expiring_mouseover',
          date: event_participant.onboarding_status[1])
      inner_class = 'fa fa-exclamation-triangle '
      text = t('event_participants.show.onboarding_status.expiring')
      status_html(outer_class, inner_class, text, tooltip)
    when :expired
      outer_class = 'red-color'
      inner_class = 'fa fa-exclamation-triangle '
      text = t('event_participants.show.onboarding_status.expired')
      status_html(outer_class, inner_class, text)
    when :rejected
      outer_class = 'red-color'
      inner_class = 'fa fa-exclamation-triangle '
      text = t('event_participants.show.onboarding_status.rejected')
      status_html(outer_class, inner_class, text)
    end
  end
  instrument_method :onboarding_status_html

  def participant_dropped_status_html(e_par, event)
    return unless e_par.accepted == true &&
                  !e_par.through_to_latest_round? &&
                  event.is_rfq_event?

    status_html('red-color', 'glyphicon glyphicon-log-out', t('event_participants.show.dropped_after_rfq_round',
                                                              round_number: e_par.ep_last_event_round))
  end
  instrument_method :participant_dropped_status_html

  def participant_round_ids(event_participant)
    return [] if event_participant.nil?

    event_participant.ep_event_rounds_round_ids
  end
  memoize :participant_round_ids

  def participant_rejected_status_html(e_par, event)
    return unless e_par.accepted == false && e_par.participating != false && event.is_rfq_event?

    status_html('red-color', 'fa fa-times',
                t('event_participants.show.rejected'))
  end
  instrument_method :participant_rejected_status_html

  def registered_status_html(event_participant)
    if event_participant.user.registered[0].to_s.casecmp('yes').zero?
      status_html('green-color', 'fa fa-check',
                  t('event_participants.show.registered_html'))
    else
      status_html('red-color', 'fa fa-times',
                  t('event_participants.show.not_registered'))
    end
  end
  instrument_method :registered_status_html

  def registered_status_sim(event_participant_contact)
    contact_status = event_participant_contact.status
    case contact_status.to_s
    when 'registered'
      color = 'green-color'
      icon = 'fa fa-check'
    when 'not_yet_invited'
      color = 'red-color'
      icon = 'fa fa-times'
    when 'invited'
      color = 'green-color'
      icon = 'fa fa-check'
    else
      color = ''
      icon = ''
    end
    status_html(color, icon, t("sim_ep_contact.#{contact_status}"))
  end

  # Show the participating status for a participant. Either:
  # - Accepted invite
  # - Not accepted invite
  # - Declined invite
  # If bounced mail, we dont show not accepted as we will
  # show the email has bounced instead.
  # If "not registered" and "not accepted",
  # dont show both as it is redundant and obvious %>
  def participating_status_html(event_participant)
    case event_participant.participating
    when true
      status_html('green-color', 'fa fa-check', t('event_participants.show.accepted_invite'))
    when false
      status_html('red-color', 'fa fa-times', t('event_participants.show.declined_invite'))
    else
      if event_participant.user.registered[0].to_s.casecmp('yes').zero? && !event_participant.permanent_failure?
        status_html('red-color', 'fa fa-times', t('event_participants.show.not_accepted_invite'))
      end
    end
  end
  instrument_method :participating_status_html

  # Show the invalid email status for a participant
  # Email is invalid if we have recorded a bouce for it
  def invalid_email_status_html(event_participant)
    et = event_participant.event_email_tracking

    return unless et&.permanent_failure

    text =
      if et.status == 'dropped'
        t('event_participants.show.email_blocked')
      else
        t('event_participants.show.invalid_email')
      end
    status_html('red-color', 'fa fa-times', text)
  end
  instrument_method :invalid_email_status_html

  def all_email_tracking(event_participants, event)
    user_ids = event_participants.map(&:user_id)
    EmailTracking.where(recipient_id: user_ids, event_id: event.id,
                        status: %w[delivered opened])
                 .order('updated_at DESC')
  end
  memoize :all_email_tracking

  def email_status_html(event_participant, et)
    tracking = et.find { |e| e.recipient_id == event_participant.user_id }
    return unless tracking

    tracking_status = tracking.status
    return unless tracking_status

    status_text = t("event_participants.show.#{tracking_status.downcase}")

    status_html(
      'orange-color',
      tracking_status == 'opened' ? 'fa fa-envelope-open-o' : 'fa fa-envelope-o',
      status_text
    )
  end
  instrument_method :email_status_html

  def status_html(outer_class, inner_class, text, tooltip = nil)
    tag.div(class: outer_class, title: tooltip) do
      tag.span(class: inner_class) +
        tag.text do
          " #{text}"
        end
    end
  end

  def participant_statuses(event)
    md_status = [[t('registration'), [[t('event_participants.show.not_registered'), 0],
                                      [t('participant_database_registered'), 1]]],
                 [t('invite'),
                  [[t('event_participants.show.not_accepted_invite'), 2],
                   [t('accepted_invite'), 3],
                   [t('event_participants.show.declined_invite'), 4],
                   [t('event_participants.show.email_delivered'), 5],
                   [t('event_participants.show.email_opened'), 6]]]]
    sd_status = [[t('onboarding'), [
      [t('event_participants.show.onboarding_status.not_onboarded'), 7],
      [t('event_participants.show.onboarding_status.onboarded'), 8],
      [t('event_participants.show.onboarding_status.expiring'), 9],
      [t('event_participants.show.onboarding_status.expired'), 10],
      [t('event_participants.show.onboarding_status.rejected'), 11]
    ]]]
    if event.sandpit_event?
      md_status
    elsif event.company.sim_dojo_event
      md_status + sd_status
    else
      md_status
    end
  end

  def answer_submitted_classes(questionnaire_answer)
    questionnaire_answer_submitted = if is_sim_dojo?
                                       questionnaire_answer.can_show_answers?
                                     else
                                       questionnaire_answer&.submitted
                                     end
    if questionnaire_answer && questionnaire_answer_submitted
      color = 'green'
      fa_class = 'check'
      title = t(:submitted).capitalize
    else
      color = 'red'
      fa_class = 'times'
      title = t(:not_submitted)
    end
    [color, fa_class, title]
  end

  def answer_rfq_submitted(event)
    event_bids = event_bids_for_user(event)
    lot_ids = event.lots.ids
    event_lots_count = retrieve_event_lots_count(event, event_bids, lot_ids)
    rfq_submitted_count, filtered_event_bids = event_lot_submission(event_bids, event, lot_ids)
    blic_bids_size = filtered_event_bids.count { |blic| blic.current_state == 'new' }
    retrieve_color(rfq_submitted_count, event_lots_count, blic_bids_size)
  end

  def event_bids_for_user(event)
    Bid.where(event_id: event.id, user_id: current_user.acting_as_id)
  end

  def event_lot_submission(event_bids, event, lot_ids)
    current_round_id = event&.event_rounds&.last&.id if event.rfq?
    rfq_submitted_count = retrieve_rfq_submitted_count(event_bids, event, lot_ids, current_round_id)
    filtered_event_bids = event_bids.select { |lot| lot.event_id == event.id }
    [rfq_submitted_count, filtered_event_bids]
  end

  def retrieve_event_lots_count(event, event_bids, lot_ids)
    event.lots.count - event_bids.rejected_for_lot_and_user(lot_ids, current_user.acting_as_user.id).count
  end

  def retrieve_rfq_submitted_count(event_bids, event, lot_ids, current_round_id)
    event_bids.count do |bid|
      lot_ids.include?(bid.lot_id) &&
        (event.rfq_or_auction? || bid.qualification_bid?) &&
        rfq_submitted?(bid, current_round_id)
    end
  end

  def rfq_submitted?(bid, current_round_id)
    bid.event_round_id == current_round_id && !bid.rejected_lot
  end

  def retrieve_color(rfq_submitted_count, event_lots_count, blic_bids_size)
    if blic_bids_size.positive? || (event_lots_count != rfq_submitted_count)
      %w[red times]
    elsif event_lots_count == rfq_submitted_count
      %w[green check]
    end
  end

  def permanent_failure_email_trackings(users)
    EmailTracking.where(recipient_id: users.map(&:id),
                        permanent_failure: true, sent: true)
  end

  def user_email(event_participant)
    return if event_participant.nil?

    event_participant['user_email']
  end

  def any_active_fields(customfields)
    customfields.each do |customfield|
      return true if customfield[:active] ||
                     (!customfield[:active] &&
                     customfield[:custom_column_values][0][:name].present?)
    end
    false
  end

  def active_and_dropped_participants(event)
    eps = event.event_participants
    active_participants =
      eps.where("accepted = 1 AND (participating = 1 OR participating IS NULL) AND EXISTS
                (SELECT event_participant_event_rounds.id FROM event_participant_event_rounds WHERE
                event_participant_id = event_participants.id AND event_round_id =
                (SELECT MAX(event_rounds.id) FROM event_rounds WHERE event_rounds.event_id = #{event.id}))")

    query = active_participants.present? ? "id NOT IN (#{active_participants.ids.join(',')})" : '1 = 1'
    dropped_participants = eps.where("accepted = 1 AND #{query}")
    [active_participants, dropped_participants]
  end

  def event_message_params(event, message, reject_decline_event, event_participant_decline)
    if reject_decline_event
      [reject_invite_event_event_participant_path(event, event_participant_decline, message, true), :put, false]
    else
      [event_messages_path(event, message, false), :post, true]
    end
  end

  def sourcing_invited_through_options
    EventParticipant.invited_throughs.hash.map { |k, v| [t("event_participants.show.invited_through.#{k}"), v] }
  end

  def translated_invited_through(event_participant)
    return t('event_participants.show.invited_through.through_system') unless event_participant&.invited_through?

    t("event_participants.show.invited_through.#{event_participant.invited_through}")
  end

  def supporting_collaborator?(event_participant, event)
    return false unless event.company.supplier_collaboration_enabled?

    event_participant.user_supporting_collaborator?(current_user.id)
  end
end
