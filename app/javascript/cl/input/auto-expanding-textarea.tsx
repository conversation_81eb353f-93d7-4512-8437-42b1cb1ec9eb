import React, { useEffect, useRef, forwardRef } from "react";
import { Textarea, type TextareaProps } from "./textarea";
import { mergeRefs } from "@/lib/dom";
import type { HelpfulOmit } from "@/types/util";
import clsx from "clsx";

interface AutoExpandingTextareaProps extends HelpfulOmit<TextareaProps, "onInput"> {
  rowLimit: number;
}

export const AutoExpandingTextarea = forwardRef<HTMLTextAreaElement, AutoExpandingTextareaProps>((props, ref) => {
  const { rowLimit, className, ...rest } = props;
  const internalRef = useRef<HTMLTextAreaElement>(null);
  const handleInput = useHandleInput(internalRef, rowLimit);

  // Reset height when prompt is empty
  useEffect(() => {
    const element = internalRef.current;
    if (element && props.value === "") element.style.height = "auto";
  }, [props.value, internalRef]);

  return (
    <Textarea
      {...rest}
      ref={mergeRefs(internalRef, ref)}
      onInput={handleInput}
      className={clsx("cl-auto-expanding", className)}
    />
  );
});

AutoExpandingTextarea.displayName = "AutoExpandingTextarea";

const useHandleInput = (textareaRef: React.RefObject<HTMLTextAreaElement>, rowLimit: number) => {
  const element = textareaRef.current;
  if (!element) return;

  return () => {
    element.style.height = "auto";

    const lineHeight = parseFloat(getComputedStyle(element).lineHeight);
    const maxHeight = lineHeight * rowLimit;

    if (element.scrollHeight < maxHeight) {
      element.style.height = `${element.scrollHeight}px`;
    } else {
      element.style.height = `${maxHeight}px`;
    }
  };
};
