// #ENTRYPOINT
import React from "react";
import { Provider } from "react-redux";
import { getStore } from "@/store";
import { useAppDispatch, useAppSelector } from "@/hooks/redux";
import { selectNotificationBanners, bannerRemoved, type Banner } from "@/slices/notifications";
import DOMPurify from "dompurify";

const OFFLINE_SUFFIXES = ["/advanced_lots", "/advanced_lots/monitor", "/lots", "/lots/monitor"] as const;
const ALLOWED_TAGS = ["b", "strong", "i", "em", "br"];

const sanitiseMsg = (raw: string): string => {
  return DOMPurify.sanitize(raw, {
    ALLOWED_TAGS,
    ALLOWED_ATTR: [],
  });
};

// exporting the inner component so tests can inject a fresh store (suggestion from BD)
export const NotificationCenterInner: React.FC = () => {
  const dispatch = useAppDispatch();
  const banners = useAppSelector(selectNotificationBanners);

  const pathname = typeof window !== "undefined" ? window.location.pathname : "";
  const isOfflineAllowed = React.useMemo(() => OFFLINE_SUFFIXES.some((s) => pathname.endsWith(s)), [pathname]);

  return (
    <div
      aria-live="polite"
      aria-atomic="true"
      style={{
        top: "75px", // room for nav bar
        left: "1rem", // small gap from the left edge
        zIndex: 1050,
        maxWidth: "28rem",
        position: "fixed",
      }}
    >
      {banners.map((banner: Banner) => {
        if (banner.id === "offline" && !isOfflineAllowed) return null;

        const bsVariant = banner.variant === "error" ? "danger" : banner.variant;

        return (
          <div key={banner.id} className="animated fadeInUp position-relative mb-10 md-bs5">
            <div className="bg-white d-flex px-1 py-2 rounded shadow">
              <div
                style={{
                  backgroundColor: `var(--bs-${bsVariant})`,
                  flex: "0 0 6px",
                }}
                className="me-3 ms-1 rounded"
              ></div>
              <div className="flex-grow-1">
                {banner.title && <h5 className="fs-16 fw-semibold">{banner.title}</h5>}
                <p className="mb-0" dangerouslySetInnerHTML={{ __html: sanitiseMsg(banner.message) }} />
              </div>
              <button
                type="button"
                className="btn-close fs-12 me-1 ms-2 flex-shrink-0"
                aria-label="Close"
                onClick={() => {
                  dispatch(bannerRemoved(banner.id));
                }}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
};

const NotificationCenter: React.FC = () => (
  <Provider store={getStore()}>
    <NotificationCenterInner />
  </Provider>
);

export default NotificationCenter;
