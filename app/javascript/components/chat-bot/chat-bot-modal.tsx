import React from "react";
import { But<PERSON> } from "@/cl/button";
import styles from "./index.module.scss";

interface ChatBotModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  children: React.ReactNode;
}

export default function ChatBotModal({ isOpen, onClose, onConfirm, title, children }: ChatBotModalProps) {
  if (!isOpen) return null;

  return (
    <div className={styles.chatBotModalOverlay} onClick={onClose}>
      <div className={styles.chatBotModal} onClick={(e) => e.stopPropagation()}>
        <div className={styles.chatBotModalHeader}>
          <h4 className={styles.chatBotModalTitle}>{title}</h4>
          <button className={styles.chatBotModalClose} onClick={onClose}>
            ×
          </button>
        </div>
        <div className={styles.chatBotModalBody}>{children}</div>
        <div className={styles.chatBotModalActions}>
          <Button brand="outlined-primary" onClick={onClose}>
            Cancel
          </Button>
          <Button brand="primary" onClick={onConfirm}>
            Delete
          </Button>
        </div>
      </div>
    </div>
  );
}
