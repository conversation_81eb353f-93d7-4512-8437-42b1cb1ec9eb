import React, { useEffect } from "react";
import { fetchChatHistory, selectChatOpen } from "@/slices/chat-bot";
import { useAppDispatch, useAppSelector } from "@/hooks/redux";
import { useToggle } from "@/hooks/use-toggle";
import { But<PERSON> } from "@/cl/button";
import chatBotIcon from "@images/icons/chat-bot.svg";
import ChatMessages from "./chat-messages";
import ChatList from "./chat-list";
import ChatBotHeader from "./chat-bot-header";
import ChatBotFooter from "./chat-bot-footer";
import { t } from "@/i18n";
import styles from "./index.module.scss";

export default function ChatBotWrapper() {
  const [openChatBot, chatBotToggler] = useToggle(false);
  const title = t("ask_gemini", { ns: "llm" });
  const dispatch = useAppDispatch();

  useEffect(() => {
    void dispatch(fetchChatHistory());
  }, [dispatch]);

  return (
    <div className={styles.chatBotWrapper}>
      <div className="text-end">
        <div className={styles.toggleContainer}>
          <Button
            title={title}
            brand="null"
            className={styles.toggler}
            onClick={() => chatBotToggler.set(!openChatBot)}
          >
            <img src={chatBotIcon} alt="chat-bot-icon" />
          </Button>
          <div className={styles.togglerText}>{title}</div>
        </div>
      </div>
      {openChatBot && <ChatBotContainer />}
    </div>
  );
}

export function ChatBotContainer() {
  const chatOpen = useAppSelector(selectChatOpen);

  return (
    <div className={styles.chatBotContainer}>
      <ChatBotHeader />
      <div className={styles.chatBotCard} id="chat-bot-card">
        <div className={styles.chatBotBody}>{chatOpen ? <ChatMessages /> : <ChatList />}</div>
        <ChatBotFooter />
      </div>
    </div>
  );
}
