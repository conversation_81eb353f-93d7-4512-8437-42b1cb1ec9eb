import React, { useEffect, useState } from "react";
import { deleteChat, fetchChat, selectOrderedChatHistory } from "@/slices/chat-bot";
import { useAppDispatch, useAppSelector } from "@/hooks/redux";
import { Button } from "@/cl/button";
import { Ellipsis } from "@/cl/ellipsis";
import { getTimeDifference } from "./helpers";
import { Icon } from "@/cl/icon";
import Modal from "@/cl/modal";
import styles from "./index.module.scss";
import { useToggle } from "@/hooks/use-toggle";
import type { PreviousChat } from "@/slices/chat-bot/types";

export default function ChatList() {
  const chatHistory = useAppSelector(selectOrderedChatHistory);

  return (
    <>
      {chatHistory.map((chat) => (
        <ChatListItem chat={chat} key={chat.id} />
      ))}
    </>
  );
}

function ChatListItem({ chat }: { chat: PreviousChat }) {
  const dispatch = useAppDispatch();
  const [showOptionBtn, toggleOptionBtn] = useToggle(false);

  return (
    <div
      className={styles.chatListItem}
      key={chat.id}
      onMouseEnter={toggleOptionBtn}
      onMouseLeave={() => toggleOptionBtn.set(false)}
    >
      <Button brand="null" className={styles.itemTitle} onClick={() => void dispatch(fetchChat(chat.id))}>
        <Ellipsis maxWidth={200}>{chat.title}</Ellipsis>
      </Button>
      <TimeSinceLastMessage lastUpdated={chat.updated_at} />
      {showOptionBtn && <ChatListOptions chat={chat} />}
    </div>
  );
}

function ChatListOptions({ chat }: { chat: PreviousChat }) {
  const [showOptions, toggleOptions] = useToggle(false);

  return (
    <div style={{ position: "relative" }}>
      <Button
        brand="null"
        type="button"
        className="px-2"
        onClick={() => toggleOptions.set(!showOptions)}
        aria-label="Delete chat"
        aria-describedby={`deleteChat-${chat.id}`}
      >
        <Icon name="three-dots" />
      </Button>
      {showOptions && <ChatOptions chatId={chat.id} />}
    </div>
  );
}

function ChatOptions({ chatId }: { chatId: number }) {
  const dispatch = useAppDispatch();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleDelete = () => {
    void dispatch(deleteChat(chatId));
    setShowDeleteModal(false);
  };

  return (
    <>
      <div className={styles.chatOptions}>
        <Button brand="null" type="button" onClick={() => setShowDeleteModal(true)}>
          Delete <Icon name="trash-fill" />
        </Button>
      </div>

      <Modal
        isOpen={showDeleteModal}
        onRequestClose={() => setShowDeleteModal(false)}
        title="Delete Chat"
        parentSelector={() => document.getElementById("chat-bot-card") || document.body}
        actions={
          <>
            <Button brand="outlined-primary" onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Button>
            <Button brand="primary" onClick={handleDelete}>
              Delete
            </Button>
          </>
        }
      >
        Are you sure you want to delete this chat? This action cannot be undone.
      </Modal>
    </>
  );
}

export function TimeSinceLastMessage({ lastUpdated }: { lastUpdated: string }) {
  const [elapsedTime, setElapsedTime] = useState(() => getTimeDifference(lastUpdated));

  useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(getTimeDifference(lastUpdated));
    }, 60 * 1000);

    return () => clearInterval(interval);
  }, [lastUpdated]);

  return <div className={styles.elapsedTime}>{elapsedTime}</div>;
}
