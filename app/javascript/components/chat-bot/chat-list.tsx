import React, { useEffect, useState } from "react";
import { deleteChat, fetchChat, selectOrderedChatHistory } from "@/slices/chat-bot";
import { useAppDispatch, useAppSelector } from "@/hooks/redux";
import { Button } from "@/cl/button";
import { Ellipsis } from "@/cl/ellipsis";
import { getTimeDifference } from "./helpers";
import { Icon } from "@/cl/icon";
import { useToggle } from "@/hooks/use-toggle";
import { confirm } from "./confirm";
import type { PreviousChat } from "@/slices/chat-bot/types";
import styles from "./index.module.scss";

export default function ChatList() {
  const chatHistory = useAppSelector(selectOrderedChatHistory);

  return (
    <div className={styles.chatList}>
      {chatHistory.map((chat) => (
        <ChatListItem chat={chat} key={chat.id} />
      ))}
    </div>
  );
}

function ChatListItem({ chat }: { chat: PreviousChat }) {
  const dispatch = useAppDispatch();
  const [showOptionBtn, toggleOptionBtn] = useToggle(false);

  return (
    <div
      className={styles.chatListItem}
      key={chat.id}
      onMouseEnter={toggleOptionBtn}
      onMouseLeave={() => toggleOptionBtn.set(false)}
    >
      <Button brand="null" className={styles.itemTitle} onClick={() => void dispatch(fetchChat(chat.id))}>
        <Ellipsis maxWidth={200}>{chat.title}</Ellipsis>
      </Button>
      <TimeSinceLastMessage lastUpdated={chat.updated_at} />
      {showOptionBtn && <ChatListOptions chat={chat} />}
    </div>
  );
}

function ChatListOptions({ chat }: { chat: PreviousChat }) {
  const [showOptions, toggleOptions] = useToggle(false);

  return (
    <div style={{ position: "relative" }}>
      <Button
        brand="null"
        type="button"
        className="px-2"
        onClick={() => toggleOptions.set(!showOptions)}
        aria-label="Delete chat"
        aria-describedby={`deleteChat-${chat.id}`}
      >
        <Icon name="three-dots" />
      </Button>
      {showOptions && <ChatOptions chat={chat} />}
    </div>
  );
}

function ChatOptions({ chat }: { chat: PreviousChat }) {
  const dispatch = useAppDispatch();

  return (
    <div className={styles.chatOptions}>
      <Button
        brand="null"
        type="button"
        onClick={() =>
          confirm({
            title: "Delete chat?",
            body: `This will delete "${chat.title}"`,
            confirmBtnText: "Delete chat",
          }).accept(() => void dispatch(deleteChat(chat.id)))
        }
      >
        Delete <Icon name="trash-fill" />
      </Button>
    </div>
  );
}

export function TimeSinceLastMessage({ lastUpdated }: { lastUpdated: string }) {
  const [elapsedTime, setElapsedTime] = useState(() => getTimeDifference(lastUpdated));

  useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(getTimeDifference(lastUpdated));
    }, 60 * 1000);

    return () => clearInterval(interval);
  }, [lastUpdated]);

  return <div className={styles.elapsedTime}>{elapsedTime}</div>;
}
