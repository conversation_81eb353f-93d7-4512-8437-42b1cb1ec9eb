import React from "react";
import { But<PERSON> } from "@/cl/button";
import renderProp from "@/utils/render-prop";
import { createConfirm, type ConfirmComponentProps, type CreateConfirmOptions } from "@/utils/confirm";
import { t } from "@/i18n";
import styles from "./confirm.module.scss";

function Confirm({ isOpen, title, body, confirmBtnText, reject, confirm }: ConfirmComponentProps) {
  console.log("isOpen: ", isOpen);
  if (!isOpen) return null;

  return (
    <div className={styles.confirmOverlay}>
      <div className={styles.confirmContent}>
        <div className={styles.title}>{renderProp(title || t("confirmation_popup.please_confirm", { ns: "llm" }))}</div>
        <div className={styles.body}>{renderProp(body)}</div>
        <div className={styles.actions}>
          <Button brand="outlined-primary" onClick={reject}>
            Cancel
          </Button>
          <Button brand="primary" onClick={confirm}>
            {confirmBtnText ?? t("confirmation_popup.confirm", { ns: "llm" })}
          </Button>
        </div>
      </div>
    </div>
  );
}

export const confirm = (options: CreateConfirmOptions) => {
  // Make sure we are rendering the confirmation popup inside the chat-bot
  // widget
  options.parentElement = document.getElementById("chat-bot-card") || undefined;

  return createConfirm(Confirm, options);
};

export default Confirm;
