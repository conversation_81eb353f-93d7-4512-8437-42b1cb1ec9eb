import { createAppSlice } from "@/slices/util/createAppSlice";
import { createCableThunk, type CableThunk } from "@/lib/stream";
import { createEntityAdapter, createSelector, type ActionReducerMapBuilder, type WithSlice } from "@reduxjs/toolkit";
import type { ChatBotState, ChatChunk, ChatMessage, ChatResult, ContinuedPrompt, PreviousChat, Prompt } from "./types";
import { customFetch } from "@/lib/fetch";
import { safeAssign } from "@/utils";
import pick from "lodash/pick";
import omit from "lodash/omit";

export const chatBotMessageAdapter = createEntityAdapter({
  selectId: (message: ChatMessage) => message.id,
});

const initialState: ChatBotState = {
  id: null,
  title: "",
  messages: chatBotMessageAdapter.getInitialState(),
  isStreaming: false,
  error: null,
  history: [],
  chatOpen: false,
};

export const createNewChat = createCableThunk<ChatChunk, ChatResult, Prompt>("chatBot/createNewChat", (arg) => ({
  url: "/llm/api/v1/chats",
  method: "POST",
  body: JSON.stringify({ chat: { prompt: arg.prompt } }),
  headers: {
    "Content-Type": "application/json",
  },
}));

export const continueChat = createCableThunk<ChatChunk, ChatResult, ContinuedPrompt>("chatBot/continueChat", (arg) => ({
  url: `/llm/api/v1/chats/${arg.chatId}`,
  method: "PUT",
  body: JSON.stringify({ chat: { prompt: arg.prompt } }),
  headers: {
    "Content-Type": "application/json",
  },
}));

const addChatBotThunkCases = (
  builder: ActionReducerMapBuilder<ChatBotState>,
  thunk: CableThunk<ChatChunk, ChatResult, Prompt> | CableThunk<ChatChunk, ChatResult, ContinuedPrompt>
) => {
  builder
    .addCase(thunk.pending, (state, { meta }) => {
      state.isStreaming = true;
      state.error = null;
      if (state.id === null) {
        state.chatOpen = true;
        state.title = meta.arg.prompt;
      }
    })
    .addCase(thunk.received, (state, { payload }) => {
      if (payload.type !== "CableMessages::LLM::ChunkMessage") return;
      const { id, role, content, created_at, updated_at } = payload;

      const message = state.messages.entities[id];
      if (message) {
        message.content += content;
      } else {
        chatBotMessageAdapter.addOne(state.messages, { id, role, content, created_at, updated_at });
      }
    })
    .addCase(thunk.fulfilled, (state, { payload }) => {
      state.isStreaming = false;

      if (state.id === null) {
        state.id = payload.id;
      }

      // Update the chat history
      const chatInHistory = state.history.find((chat) => chat.id === state.id);
      if (chatInHistory) {
        chatInHistory.created_at = payload.created_at;
        chatInHistory.updated_at = payload.updated_at;
      } else {
        const { id, title, created_at, updated_at } = payload;
        state.history.push({ id, title, created_at, updated_at });
      }
    })
    .addCase(thunk.rejected, (state, action) => {
      state.isStreaming = false;
      state.error = action.error.message || "Failed to stream response";
      if (state.id === null) state.title = initialState.title;
    });
};

export const chatBotSlice = createAppSlice({
  name: "chatBot",
  initialState,
  reducers: (create) => ({
    newChat: create.reducer((state) => {
      safeAssign(state, omit(initialState, "chatOpen", "history"));
    }),
    resetChatBotState: create.reducer((state) => {
      safeAssign(state, omit(initialState, "history"));
    }),
    fetchChat: create.asyncThunk(
      async (chatId: number, { signal, requestId }) => {
        const response = await customFetch.get(`/llm/api/v1/chats/${chatId}`, { signal, requestId });
        return (await response.json()) as ChatResult;
      },
      {
        fulfilled(state, { payload }) {
          safeAssign(state, pick(payload, "id", "title"));
          state.chatOpen = true;
          chatBotMessageAdapter.setAll(state.messages, payload.messages);
        },
        rejected(state, action) {
          state.error = action.error.message || "Chat not found";
        },
      }
    ),
    deleteChat: create.asyncThunk(
      async (chatId: number, { signal, requestId }) => {
        await customFetch.delete(
          {
            url: `/llm/api/v1/chats/${chatId}`,
            body: { chat_id: chatId },
          },
          { signal, requestId }
        );
        return chatId;
      },
      {
        fulfilled(state, { payload }) {
          state.history = state.history.filter((chat) => chat.id !== payload);
        },
        rejected(state, action) {
          state.error = action.error.message || "Can't delete this chat";
        },
      }
    ),
    fetchChatHistory: create.asyncThunk(
      async (_, { signal, requestId }) => {
        const response = await customFetch.get("/llm/api/v1/chats", { signal, requestId });
        return (await response.json()) as PreviousChat[];
      },
      {
        fulfilled(state, { payload }) {
          state.history = payload;
        },
        rejected(state, action) {
          state.error = action.error.message || "Failed to fetch chat history";
        },
      }
    ),
  }),

  selectors: {
    selectChatId: (state) => state.id,
    selectChatTitle: (state) => state.title,
    selectChatHistory: (state) => state.history,
    selectIsStreaming: (state) => state.isStreaming,
    selectError: (state) => state.error,
    selectChatOpen: (state) => state.chatOpen,
  },

  extraReducers(builder) {
    addChatBotThunkCases(builder, continueChat);
    addChatBotThunkCases(builder, createNewChat);
  },
});

export const { newChat, resetChatBotState, fetchChat, deleteChat, fetchChatHistory } = chatBotSlice.actions;
export const { selectChatId, selectChatTitle, selectChatHistory, selectIsStreaming, selectError, selectChatOpen } =
  chatBotSlice.selectors;
export const {
  selectAll: selectAllMessages,
  selectById: selectMessageById,
  selectIds: selectMessageIds,
  selectEntities: selectMessageEntities,
  selectTotal: selectTotalMessages,
} = chatBotMessageAdapter.getSelectors((state: WithSlice<typeof chatBotSlice>) => state.chatBot.messages);

export const selectOrderedChatHistory = createSelector(selectChatHistory, (history) => {
  return [...history].sort((a, b) => b.updated_at.localeCompare(a.updated_at));
});
