import type { RootState } from "@/store";
import type { Filters, ComparisonFilter } from "./types";
import { ColumnType } from "./types";
import { selectAllFilters, selectCustomFilters, selectListingSearch } from ".";
import { customFetch } from "@/lib/fetch";
import type { AsyncThunkPayloadCreator } from "@reduxjs/toolkit";
import type { Contract } from "@/common-prop-types";
import { selectionAsArraySelector } from "@/slices/contracts/listing/selectors";

export type FilterResponse = {
  filtered: boolean;
  selected_contract_ids: number[];
};

const applyEqualityFilters = (filters: Partial<Record<string, unknown>>, params: Record<string, unknown>) => {
  Object.assign(params, filters);
};

const applyComparisonFilters = (
  filters: Partial<Record<string, ComparisonFilter<string | number>>>,
  params: Record<string, unknown>
) => {
  for (const [key, filter] of Object.entries(filters)) {
    if (!filter) continue;
    params[key] = filter.value;
    params[`${key}_comparison`] = filter.type;
  }
};

const applyMultiPickFilters = (filters: Partial<Record<string, unknown[]>>, params: Record<string, unknown>) => {
  for (const [key, values] of Object.entries(filters)) {
    if (!values) continue;
    params[key] = values.map((text) => ({ id: text, text }));
  }
};

const applyFilters = (filters: Filters) => {
  const params: Record<string, unknown> = {};
  applyEqualityFilters(filters[ColumnType.Text], params);
  applyComparisonFilters(filters[ColumnType.Numeric], params);
  applyComparisonFilters(filters[ColumnType.Date], params);
  applyMultiPickFilters(filters[ColumnType.Checkbox], params);
  applyEqualityFilters(filters[ColumnType.SinglePick], params);
  applyMultiPickFilters(filters[ColumnType.MultiPick], params);

  // HACK: Change the names of the posted params. The backend expects a
  // different shape back than they give us.
  const { contract_number, supplier, stakeholder_user_name, contact_company_name, ...amendedParams } = params;
  if (contract_number) amendedParams["reference"] = contract_number;
  if (supplier) amendedParams["suppliers"] = supplier;
  if (contact_company_name) amendedParams["contact_company_names"] = contact_company_name;
  if (stakeholder_user_name) amendedParams["stakeholders"] = stakeholder_user_name;

  return amendedParams;
};

interface FilterContractsRequest {
  search: string;
  attributes: Record<string, unknown>;
  custom_columns: Record<string, unknown>;
}

export const filterContracts: AsyncThunkPayloadCreator<FilterResponse, void> = async (
  _,
  { getState, requestId, signal }
) => {
  const state = getState() as RootState;

  const params: FilterContractsRequest = {
    search: selectListingSearch(state),
    attributes: applyFilters(selectAllFilters(state)),
    custom_columns: applyFilters(selectCustomFilters(state)),
  };

  const response = await customFetch.post(
    {
      url: "/contracts/filter_contracts",
      body: params,
    },
    { requestId, signal }
  );

  return (await response.json()) as FilterResponse;
};

interface ToggleArchivedOrDeleteResponse {
  new_state: string;
  contract_archived_destroy_status: string;
  status: string;
}

export const toggleArchived: AsyncThunkPayloadCreator<ToggleArchivedOrDeleteResponse, number> = async (
  contractId,
  { signal, requestId }
) => {
  const response = await customFetch.delete(
    {
      url: `/contracts/${contractId}/mark_archive`,
      body: { contract_id: contractId },
    },
    { signal, requestId }
  );
  return (await response.json()) as ToggleArchivedOrDeleteResponse;
};

interface ChangeOwnerRequest {
  target_owner_id: number;
  contract_ids: number[];
}

interface ChangeOwnerResponse {
  contracts: Contract[];
}

export const changeOwner: AsyncThunkPayloadCreator<ChangeOwnerResponse, number> = async (
  target_owner_id,
  { signal, requestId, getState }
) => {
  const state = getState() as RootState;
  const selectedIds = selectionAsArraySelector(state.contracts.listings);
  const response = await customFetch.post(
    {
      url: `/contracts/change_contracts_owner`,
      body: { target_owner_id, contract_ids: selectedIds } satisfies ChangeOwnerRequest,
    },
    { signal, requestId }
  );
  return (await response.json()) as ChangeOwnerResponse;
};
