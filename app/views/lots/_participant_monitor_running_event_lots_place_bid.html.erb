<%
rejected_lot = false
# If we have a bid this was triggered after bid.create rather than lots.monitor...
if (!@bid.nil? && (@bid.user_id == current_user.acting_as_id))
  rejected_lot = (@bid && @bid.lot_id == lot.id && @bid.rejected_lot && !@bid.id.nil?)
else
  rejected_lot = ((lot.this_participant_rejected_lot(current_user.acting_as_id)))
end
  supporting_collaborator = supporting_collaborator?(@event_participant || @bid.event_participant, @event)
%>
<% if !rejected_lot && !lot.is_event_total? %>
  <% if (["running","pause"].include?(event.current_state))  %>
    <% if event.event_type == "Japanese" %>
      <%  bid_in_round = false
          timed_out_or_declined = false
          if (!@bid.nil? && (@bid.user_id == current_user.acting_as_id))
            bid_in_round = (@bid && (@bid.lot_id == lot.id) && (lot.bid_round_price == @bid.host_price) && !@bid.id.nil?)
            timed_out_or_declined = @bid.decline_bid
          else
            bid_in_round = (lot.participant_bid_in_round(current_user.acting_as_id) )
          end
      %>
      <% if bid_in_round %>
        <%=t(:bid_placed) %>
      <% elsif (timed_out_or_declined || !lot.participant_active_in_japanese_auction(current_user.acting_as_id)) || (event.bid_round > 1 && !lot.participant_not_timed_out_or_declined?(event.bid_round, current_user)) %>
        <%=t(:bidding_closed) %>
      <% elsif event.pause? %>
        <%= I18n.t(:event_is_paused) %>
      <% else %>
        <%= form_for([event, lot, Bid.new], remote: true, html: { class: 'pull-left' }) do |b| %>
          <%= b.error_messages %>
          <%= b.hidden_field :bid_round, value: event.bid_round %>
          <% precision = lot.fetch_dp_according_bid_range(lot.bid_round_price) %>
          <%= b.hidden_field :price, value: number_to_currency(lot.bid_round_price, format: '%n', negative_format: '-%n', precision: precision) %>
          <%= b.hidden_field :event_id, value: event.id %>
          <%= b.hidden_field :lot_id, value: lot.id %>
          <%= b.hidden_field :user_id, value: current_user.acting_as_id %>
          <%= b.hidden_field :exchange_rate_id, id: "bid_exchange_rate_id_#{lot.id}" %>
          <%= button_tag(type: 'submit', class: "japanese-btn btn btn-default light-gray-color push-5-r br-green-color", data: { confirm: t(:are_you_sure), type: 'japanese_event_lot', id: lot.id }) do  %>
            <%= (t(:accepted_symbol) + ' ' + t(:accept)).html_safe %>
          <% end %>
        <% end %>
        <%= form_for([event, lot, Bid.new], remote: true, html: { class: 'decline-right' }) do |b| %>
          <%= b.error_messages %>
          <%= b.hidden_field :bid_round, value: event.bid_round %>
          <%= b.hidden_field :decline_bid, value: true%>
          <%= b.hidden_field :event_id, value: event.id %>
          <%= b.hidden_field :lot_id, value: lot.id %>
          <%= b.hidden_field :user_id, value: current_user.acting_as_id %>
          <%= button_tag(type: 'submit', class: "japanese-btn btn btn-default light-gray-color push-5-r  br-red-color", data: { confirm: t(:are_you_sure)}) do  %>
            <%= (t(:declined_symbol) + ' ' +t(:decline)).html_safe %>
          <% end %>
        <% end %>
      <% end %>
    <% else %>
      <%if (!rejected_lot && !event.pause?)  %>
        <%= link_to (t(:accepted_symbol) + t(:enter_bid)).html_safe, new_event_lot_bid_url(event, lot, tab: "one"), remote: true, class: "btn btn-default light-gray-color push-10-r", disabled: supporting_collaborator %>
      <% elsif event.pause? %>
        <%=  I18n.t(:event_is_paused) %>
      <%end%>
    <% end %>
  <% elsif (["post_rfq_pre_auction","pending"].include?(event.current_state)) %>
    <%=t :bidding_has_not_started %>
  <% elsif event.pause? %>
    <%=  I18n.t(:event_is_paused) %>
  <% else %>
    <%=t :bidding_closed %>
  <% end %>
<% end %>

<% content_for :javascript do %>
  jQuery(function($){
    $.fn.init_bid(<%= lot.id %>);
  });
<% end %>
