<%# this_participants_first_bid = lot.this_participants_first_bid(current_user.acting_as_id) %>
<%
  this_participants_first_bid = if participants_first_bids.nil?
                                  lot.this_participants_first_bid(current_user.acting_as_id, @event_round)
                                else
                                  participants_first_bids.detect{ |bid| bid.lot_id.eql?(lot.id) }
                                end
  event_participant = @event.find_participant(current_user.acting_as_id)
  latest_bid = event_participant.latest_bid_before_auction_for_lot(lot)
  event_rounds = @event.event_rounds
  supporting_collaborator = supporting_collaborator?(event_participant, @event)
%>
<div class="table-body" id="item_<%= lot.id %>">
  <div class="table-in-form lot">
    <div id="position_of_<%=lot.id %>" class="tbody-all tbody-lot">
      <div class="col-caption hidden-lg hidden-md hidden-sm">
        <%= t :lot_position %>&nbsp
        <%=show_info('lot/position') %>
      </div>
      <%=h lot.position %>
    </div>
    <div class="tbody-all tbody-lotname">
      <div class="col-caption hidden-lg hidden-md hidden-sm">
        <%= t :lot_name %>&nbsp
        <%=show_info('lot/name') %>
      </div>
      <%= truncate_lot_name(lot.name) %>
    </div>
    <div class="tbody-all tbody-quantity">
      <div class="col-caption hidden-lg hidden-md hidden-sm">
        <%= t :quantity_uom  %>&nbsp
        <%=show_info('lot/quantity_uom') %>
      </div>
      <%=h number_with_delimiter(strip_insignificant_zeros(lot.quantity, Rails.application.config_for(:config).max_precision_for[:simple_lot_quantity])) %> x <%=h lot.unit_of_measure.name %>
    </div>
    <% prv_bid = event_participant.latest_bid_before_auction_for_lot(lot, @event_round, current_user)%>
    <% value = prv_bid.present? ? prv_bid.price : '-' %>
    <% if this_participants_first_bid.nil? %>
      <div class="tbody-all tbody-price">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%=t :your_price_per_uom %>&nbsp
          <%=show_info('lot/your_price_per_unit') %>
        </div>
        <%= prv_bid&.rejected_lot? ? '-' : show_currency_for_total(value, @event.currency) %>
      </div>
      <div class="tbody-all tbody-value">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%= t :total_lot_value_placed %>&nbsp
          <%=show_info('lot/total_lot_value_placed') %>
        </div>
        <% if prv_bid.present? && prv_bid.price.present? %>
          <%=h ComplexLotCommonHelperMethods.show_currency( (prv_bid.price * lot.quantity), @event.currency)%>
          <% if allowed_to_see_rank?(lot, @event, @event_round, event_rounds.last, latest_bid) %>
            <div>
              <b><%= t(:ranking_after_round, event_round: event_rounds.second_to_last.event_round ) %></b>
              <span class="lot-rank-symbol badge ranked<%= latest_bid.overall_rank_if_weighted %>">
                <%= latest_bid.overall_rank_if_weighted %>
              </span>
            </div>
          <% end %>
        <%else%>
          -
        <%end%>
      </div>

      <div id="place_bid" class="tbody-all tbody-status">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%=t :bid_status %>&nbsp
          <%=show_info('bid/bid_status') %>
        </div>
        <% if show_place_bid_button(lot, @event, @event_round) %>
          <% b = lot.bids.new(event_id: @event.id, lot_id: lot.id, user_id: current_user.acting_as_id) %>
          <% b.rejected_lot = true %>
          <%= form_for([@event, lot, b], remote: true) do |f| %>
            <%= f.hidden_field :rejected_lot %>
            <%= f.hidden_field :event_id %>
            <%= f.hidden_field :lot_id %>
            <%= f.hidden_field :user_id %>
            <% bid_tag = (@event_round.present? && lot.this_participants_all_bids(current_user.acting_as_id).present?) ? t(:update_bid) : t(:enter_bid)%>
            <%= link_to (t(:accepted_symbol) + ' ' + bid_tag).html_safe, new_event_lot_bid_url(@event, lot, event_round_id: @event_round), remote: true, class: "btn btn-default light-gray-color push-10-r", disabled: supporting_collaborator %>
            <%= button_tag(type: 'submit', class: "btn btn-default light-gray-color push-10-r", data: { disable_with: t(:submitting) , confirm: t(:are_you_sure)}, disabled: supporting_collaborator) do  %>
              <%= (t(:declined_symbol) + ' ' + t(:reject_lot)).html_safe %>
            <% end %>
          <% end %>
        <% elsif  @event.is_rfq_event? && rejected_lot_round_wise(lot).present? %>
          <%= t :lot_rejected %>
        <% else %>
            -
        <% end %>
      </div>
    <% elsif this_participants_first_bid && this_participants_first_bid.price && !this_participants_first_bid.rejected_lot? and (@event.start_at.blank? ? true :(this_participants_first_bid.created_at < @event.start_at)) %>
      <div class="tbody-all tbody-price">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%=t :your_price_per_uom %>&nbsp
          <%=show_info('lot/your_price_per_unit') %>
        </div>
        <%=h show_bid(this_participants_first_bid, @event, lot) %>
      </div>
      <div class="tbody-all tbody-value">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%= t :total_lot_value_placed %>&nbsp
          <%=show_info('lot/total_lot_value_placed') %>
        </div>
        <%=h ComplexLotCommonHelperMethods.show_currency( (this_participants_first_bid.price * lot.quantity), @event.currency) %>
      </div>
      <div class="tbody-all tbody-status">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%=t :bid_status %>&nbsp
          <%=show_info('bid/bid_status') %>
        </div>
        <%=t this_participants_first_bid.bid_status(lot) %>
      </div>
    <% elsif @event.start_at.blank? ? true : (this_participants_first_bid.created_at < @event.start_at)%>
      <div class="tbody-all tbody-price">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%=t :your_price_per_uom %>&nbsp
          <%=show_info('lot/your_price_per_unit') %>
        </div>
        -
      </div>
      <div class="tbody-all tbody-value">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%= t :total_lot_value_placed %>&nbsp
          <%=show_info('lot/total_lot_value_placed') %>
        </div>
        -
      </div>
      <div class="tbody-all tbody-status">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%=t :bid_status %>&nbsp
          <%=show_info('bid/bid_status') %>
        </div>
        <%=t :lot_rejected %>
      </div>
    <% else %>
      <div class="tbody-all tbody-price">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%=t :your_price_per_uom %>&nbsp
          <%=show_info('lot/your_price_per_unit') %>
        </div>
        -
      </div>
      <div class="tbody-all tbody-value">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%= t :total_lot_value_placed %>&nbsp
          <%=show_info('lot/total_lot_value_placed') %>
        </div>
        -
      </div>
      <div class="tbody-all tbody-status">
        <div class="col-caption hidden-lg hidden-md hidden-sm">
          <%=t :bid_status %>&nbsp
          <%=show_info('bid/bid_status') %>
        </div>
        -
      </div>
    <% end %>
  </div>
  <div class="lot-work-row " style="display:none">
    <div class="work-area" colspan="10"></div>
  </div>
</div>
