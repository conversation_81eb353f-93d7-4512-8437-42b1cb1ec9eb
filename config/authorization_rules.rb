# frozen_string_literal: true

authorization do
  role :Admin do
    has_permission_on :features, to: %i[index update]

    has_permission_on :email_domains, to: %i[index create update destroy promote]

    has_permission_on :users, :category_dojo_users, to: [:home]

    has_permission_on(
      :users,
      to: %i[
        read
        edit
        update
        home
        sourcing_dashboard
        change_password
        reset_two_fa_attempts
        change_password_update
        send_to_crm
        disable_google_authenticator
        enable_google_authenticator
        check_bulk_data
        bulk_csv
        upsert
        create_or_update_api_key
        lock_user_access
        unlock_user_access
      ]
    )

    has_permission_on(
      :sandpit_users,
      :document_templates,
      :resources,
      :currencies,
      :events,
      :lots,
      :documents,
      :currency_groups,
      :event_participants,
      :bids,
      :unit_sets,
      :unit_of_measures,
      :payment,
      :integrations,
      :roles,
      :surveys,
      :credits,
      :payments,
      :srm_accounts, :srm_contacts,
      to: %i[read new edit create update destroy finish_editing return_to_editing history]
    )

    has_permission_on(:roles, :unit_of_measures, to: %i[read edit update])

    has_permission_on(
      :companies,
      to: %i[
        read
        new
        edit
        create
        update
        display_settings
        destroy_display_settings_image
        configuration
        logo
        favicon
      ]
    )

    has_permission_on(
      :user_permissions,
      :category_dojo_user_permissions,
      to: %i[index new create destroy resource_by_type notification_mail transfer_superhost]
    )

    has_permission_on :sessions, to: [:impersonate]

    has_permission_on :authorizations, to: [:impersonate]

    has_permission_on :currencies, to: [:sort_currency]

    has_permission_on :currency_groups, to: [:sort_currency_group]

    has_permission_on :event_templates, to: %i[admin_index update set_global set_example]

    has_permission_on :picklists, to: %i[index update set_global]

    has_permission_on :notifications, to: %i[index show new edit create update destroy]

    has_permission_on(
      :contract_admin_actions,
      to: %i[index show bulk_csv check_bulk_data upsert destroy attachments add_attachments remove_attachment]
    )

    has_permission_on :custom_columns, to: %i[index show]
  end

  role :SuperHost do
    includes :Host
  end

  role :Host do
    has_permission_on :company_integrations, to: %i[index new] do
      if_attribute company_id: is { user.rule_for_view_integrations }
    end

    has_permission_on :company_integrations, to: %i[company_report] do
      if_attribute company_id: is { user.rule_for_view_company_report }
    end

    has_permission_on :company_integrations, to: %i[create update destroy edit] do
      if_attribute company_id: is { user.rule_for_edit_integrations }
    end

    has_permission_on :event_templates, to: %i[new use_template create_from_autoselection]

    has_permission_on :event_templates, to: %i[create destroy update] do
      if_attribute company_id: is { user.rule_for_edit_event_library }
    end

    has_permission_on :event_templates, to: [:index] do
      if_attribute company_id: is { user.rule_for_view_event_library }
    end

    has_permission_on :marketplace_categories, to: %i[index new] do
      if_attribute company_id: is { user.rule_for_view_marketplace }
    end

    has_permission_on :marketplace_categories, to: %i[create update destroy edit] do
      if_attribute company_id: is { user.rule_for_edit_marketplace }
    end

    has_permission_on :tag_categories, to: %i[new create update destroy edit] do
      if_attribute company: {
        superhost: is { user }
      }
    end

    has_permission_on :tag_categories, to: %i[new create update destroy edit] do
      if_attribute company_id: is { user.rule_for_edit_event_tagging }

      if_attribute company_id: is { user.rule_for_edit_participant_tagging }
    end

    has_permission_on :marketplace_attributes, to: %i[index] do
      if_attribute marketplace_category: {
        company_id: is { user.rule_for_view_marketplace }
      }
    end

    has_permission_on(
      :marketplace_attributes,
      to: %i[
        new
        create
        update
        destroy
        edit
        download
        upload
        show_participant_groups
        add_participant_group
        remove_participant_group
      ]
    ) do
      if_attribute marketplace_category: {
        company_id: is { user.rule_for_edit_marketplace }
      }
    end

    has_permission_on :postcodes, to: %i[upload download_participant_postcode] do
      if_attribute company_id: is { user.rule_for_view_marketplace }
    end

    has_permission_on :users, to: %i[auth_headers]

    has_permission_on(
      :users, :category_dojo_users,
      to: %i[
        index
        show
        edit
        update
        home
        sourcing_dashboard
        update_dashboard_configuration
        load_more_messages
        forgot_password
        reset_password
        change_password
        enable_google_authenticator
        disable_google_authenticator
      ]
    ) do
      if_attribute id: is { user.id }
    end

    has_permission_on(
      :user_permissions,
      :category_dojo_user_permissions,
      :sim_dojo_user_permissions,
      to: %i[index new create destroy add_user resource_by_type notification_mail transfer_superhost]
    ) do
      if_attribute company: is { user.company }
    end

    has_permission_on(
      :users,
      to: %i[
        show
        edit
        update
        home
        sourcing_dashboard
        change_password
        change_password_update
        update_dashboard_configuration
        load_more_messages
        forgot_password
        reset_password
        participant_database_filter
        dashboard_charts
        apply_filters
        create_or_update_api_key
        delete_api_key
        calendar
        redirect_to_qq
      ]
    ) do
      if_attribute id: is { user.id }
    end

    # We have a check in the controller, to stop host seeing this if they dont own the event...
    has_permission_on :users, to: [:index]

    has_permission_on :resources, to: [:show]

    has_permission_on :companies, to: %i[logo favicon]

    has_permission_on(
      :companies,
      :sim_dojo_companies,
      to: %i[
        display_settings
        update
        destroy_display_settings_image
        add_image
        enforce_locale
        show
        configuration
        custom_field_settings
      ]
    ) do
      if_attribute id: is { user.company_id }
    end

    has_permission_on :surveys, to: %i[new create] do
      if_attribute survey_subject_type: 'User',
                   survey_subject: {
                     id: is { user.id }
                   }

      if_attribute survey_subject: {
        user: is { user }
      }

      if_attribute survey_subject: {
        id: is_in { user.rule_for_show_events }
      }
    end

    has_permission_on :messages, to: %i[update_message read messages_greater_than] do
      if_attribute message_subject_type: 'InnovationTopic',
                   message_subject: {
                     user: is { user }
                   }

      if_attribute message_subject_type: 'Event',
                   message_subject: {
                     user: is { user }
                   }

      if_attribute message_subject_type: 'Event',
                   message_subject: {
                     id: is_in { user.rule_for_show_events }
                   }

      if_attribute message_subject_type: 'Event',
                   message_subject: {
                     id: is_in { user.rule_for_show_events_with_scoreable_events }
                   }
    end

    has_permission_on :messages, to: %i[messages_greater_than] do
      if_attribute id: is { 0 }
    end
    # TODO: Need to define the rules for innovation topic as well.
    has_permission_on :messages, to: %i[new create] do
      if_attribute message_subject_type: is_in { %w[InnovationTopic Event] },
                   message_subject: {
                     user: is { user }
                   }

      if_attribute message_subject_type: 'Event',
                   message_subject: {
                     id: is_in { user.rule_for_show_events }
                   }

      if_attribute message_subject_type: 'Event',
                   message_subject: {
                     id: is_in { user.rule_for_show_events_with_scoreable_events }
                   }
    end

    has_permission_on :message_recipients, to: %i[new create read update]

    has_permission_on(
      :events,
      to: %i[
        new
        create
        find_clone
        new_event_check
        mark_as_flash_message
        end_rfq
        warn_host
        clone_event
        event_template_autoselection
      ]
    )

    has_permission_on :events, to: [:completed_event_by_host] do
      if_attribute user: is { user },
                   sandpit_event: is { user.in_sandpit? }

      if_attribute id: is_in { user.rule_for_edit_events },
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: %i[add_custom_column_value] do
      if_attribute user: is { user },
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: %i[save_custom_column_values] do
      if_attribute user: is { user },
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: %i[resync_lots] do
      if_attribute user: is { user },
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:questionnaire_autoselection_unpersisted]

    has_permission_on :events, to: %i[questionnaire_autoselection] do
      if_attribute user: is { user },
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:clone] do
      if_attribute user: is { user }

      if_attribute id: is_in { user.rule_for_clone_events }
    end

    has_permission_on :events, to: [:show_clone_events] do
      if_attribute user: is { user },
                   current_state: %w[new pending edit running completed rfq post_rfq_pre_auction pause closed]

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_show_events },
                   current_state: %w[new pending edit running completed rfq post_rfq_pre_auction pause closed]
    end

    has_permission_on :events, to: %i[arrange index show sandpit_new_url] do
      if_attribute user: is { user },
                   current_state: %w[new pending edit running completed rfq post_rfq_pre_auction pause],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_show_events_with_scoreable_events },
                   current_state: %w[new pending edit running completed rfq post_rfq_pre_auction pause],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:return_to_editing] do
      if_attribute user: is { user },
                   current_state: %w[new pending edit running completed rfq post_rfq_pre_auction pause],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   current_state: %w[new pending edit running completed rfq post_rfq_pre_auction pause],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:close_event] do
      if_attribute user: is { user }, current_state: ['completed'], sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   current_state: ['completed'],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:re_open_event] do
      if_attribute user: is { user },
                   current_state: ['closed'],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   current_state: ['closed'],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:read] do
      if_attribute user: is { user },
                   current_state: ['closed'],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_show_events },
                   current_state: ['closed'],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: %i[finish_editing checkbox] do
      if_attribute user: is { user },
                   current_state: %w[new edit],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   current_state: %w[new edit],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:update_participant_documents_permission] do
      if_attribute user: is { user },
                   current_state: %w[new edit],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_show_events },
                   current_state: %w[new edit],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:start_auction_for_sandpit] do
      if_attribute user: is { user },
                   current_state: %w[pending post_rfq_pre_auction],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_show_events },
                   current_state: %w[pending post_rfq_pre_auction],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: %i[destroy] do
      if_attribute user: is { user },
                   current_state: %w[new edit rfq pending post_rfq_pre_auction running completed pause],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   current_state: %w[new edit rfq pending post_rfq_pre_auction running completed pause],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: %i[edit update change_bidding_level use_template lot_autoselection] do
      if_attribute user: is { user },
                   current_state: %w[new edit pause running],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   current_state: %w[new edit pause running],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:read] do
      if_attribute user: is { user },
                   current_state: ['cancelled'],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_show_events },
                   current_state: ['cancelled'],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on(
      :events,
      to: %i[
        change_event_state
        change_event_time_interval
        mark_as_flash_message
        create_bid_control
      ]
    ) do
      if_attribute user: is { user },
                   current_state: %w[running pause],
                   sandpit_event: is { user.in_sandpit? }

      # allowed from the admin permissions section
      if_attribute id: is_in { user.rule_for_edit_events },
                   current_state: %w[running pause],
                   sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on :events, to: [:subscribe_host_charts] do
      if_attribute user: is { user }

      if_attribute id: is_in { user.rule_for_show_events }
    end

    has_permission_on :events, to: [:lots_for_saving_template] do
      if_attribute user: {
        company_id: is { user.rule_for_edit_lot_library }
      }
    end

    has_permission_on :event_rounds, to: %i[new create show] do
      if_attribute event: {
        user: is { user },
        current_state: %w[completed],
        event_category_rfq: true,
        sandpit_event: is { user.in_sandpit? }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_edit_events },
        current_state: %w[completed],
        event_category_rfq: true,
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on :lots, to: %i[new update_remote]

    has_permission_on(
      :lots,
      to: %i[
        arrange
        read
        monitor
        monitor_status
        sort
        award
        change_lot_details_with_participant
        analysis
        show_info_award_notice
      ]
    ) do
      if_attribute event: {
        user: is { user },
        sandpit_event: is { user.in_sandpit? }
      }

      if_attribute event: {
        id: is_in { user.rule_for_show_events },
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on :lots, to: %i[save_awarded_participant send_award_notice] do
      if_attribute event: {
        user: is { user },
        current_state: %w[completed closed],
        sandpit_event: is { user.in_sandpit? }
      }

      if_attribute event: {
        id: is_in { user.rule_for_edit_events },
        current_state: %w[completed closed],
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on :lots, to: [:subscribe_host_charts] do
      if_attribute event: { user: is { user } }

      if_attribute event: { id: is_in { user.rule_for_show_events } }
    end

    has_permission_on :lots, to: %i[edit create update destroy upload lot_total_reference destroy_all] do
      if_attribute event: {
        user: is { user },
        current_state: %w[new edit completed],
        sandpit_event: is { user.in_sandpit? }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_edit_events },
        current_state: %w[new edit completed],
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on :event_reports, to: %i[index create update] do
      if_attribute event: {
        user: is { user },
        sandpit_event: is { user.in_sandpit? }
      }

      if_attribute event: {
        id: is_in { user.rule_for_show_events },
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on :lot_components, to: [:new]

    has_permission_on :lot_components, to: [:read] do
      if_permitted_to :read, :lot
    end

    has_permission_on :lot_components, to: %i[edit create update destroy upload] do
      if_permitted_to :edit, :lot
    end

    has_permission_on :line_items, to: [:new]

    has_permission_on :line_items, to: [:read] do
      if_permitted_to :read, :lot
    end

    has_permission_on :line_items, to: %i[edit create update destroy upload] do
      if_permitted_to :edit, :lot
    end

    has_permission_on :line_item_components, to: [:read] do
      if_permitted_to :read, :line_item
    end

    has_permission_on :line_item_components, to: %i[new edit create update destroy upload] do
      if_permitted_to :edit, :lot_component
    end

    has_permission_on :documents, to: %i[new upload_multiple_documents_for_questionnaire_questions_answer sort destroy]

    has_permission_on :documents, to: %i[arrange read zipped_documents] do
      if_attribute event: {
        user: is { user },
        sandpit_event: is { user.in_sandpit? }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_show_events },
        sandpit_event: is { user.in_sandpit? }
      }

      if_attribute event: {
        questionnaires: {
          id: is_in { user.rule_for_scorer_questionnaire_sections(@object) }
        }
      }
    end

    has_permission_on(
      :documents,
      to: %i[
        edit
        create
        update
        destroy
        create_documents_from_document_template
        create_from_automatic_document_selection
      ]
    ) do
      if_attribute event: {
        current_state: %w[new edit current],
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_edit_events },
        current_state: %w[new edit current]
      }

      if_attribute user_id: is_not { user.acting_as_id },
                   questionnaire_questions_answer_id: is_not { nil }
    end

    # Need to check
    has_permission_on :event_participants, to: %i[arrange read update_remote select_inviting_options] do
      if_attribute event: {
        user: is { user },
        sandpit_event: is { user.in_sandpit? }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_show_events },
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on :event_participants, to: %i[accept reject new rejection_note] do
      if_attribute event: {
        user: is { user },
        sandpit_event: is { user.in_sandpit? }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_edit_events },
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on(
      :event_participants,
      to: %i[
        edit
        create
        update
        upload
        create_from_group
        create_from_company_database
        create_from_automatic_supplier_selection
        warning_automatic_supplier_selection
      ]
    ) do
      if_attribute event: {
        current_state: %w[new edit],
        sandpit_event: is { user.in_sandpit? },
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        current_state: %w[new edit],
        sandpit_event: is { user.in_sandpit? },
        id: is_in { user.rule_for_edit_events }
      }
    end

    # Separated the destroy action because now for deleting the event participant in MD is based on feature switch.
    has_permission_on :event_participants, to: %i[destroy] do
      if_attribute event: {
        current_state: %w[new edit],
        sandpit_event: is { user.in_sandpit? },
        user: is { user.permit_to_destroy_bid_or_event_participant }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        current_state: %w[new edit],
        sandpit_event: is { user.in_sandpit? },
        id: is_in { user.rule_for_destroy_bids_or_event_participant }
      }
    end

    has_permission_on :event_participants, to: [:resend_invite_to_participant] do
      if_attribute event: {
        sandpit_event: is { user.in_sandpit? },
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        sandpit_event: is { user.in_sandpit? },
        id: is_in { user.rule_for_edit_events }
      }
    end

    has_permission_on :notes, to: %i[new]

    has_permission_on :notes, to: %i[read] do
      if_attribute notable_type: 'Event', notable: {
        sandpit_event: is { user.in_sandpit? },
        user: is { user }
      }

      if_attribute notable_type: 'Event', notable: {
        id: is_in { user.rule_for_show_events },
        sandpit_event: is { user.in_sandpit? }
      }

      if_attribute notable_type: 'Event', notable: {
        sandpit_event: is { user.in_sandpit? },
        questionnaires: {
          id: is_in { user.rule_for_questionnaires_for_scorers }
        }
      }

      if_attribute notable_type: 'EventParticipant', notable: {
        event: {
          sandpit_event: is { user.in_sandpit? },
          user: is { user }
        }
      }

      if_attribute notable_type: 'EventParticipant', notable: {
        event: {
          sandpit_event: is { user.in_sandpit? },
          id: is_in { user.rule_for_show_events }
        }
      }

      if_attribute notable_type: 'EventParticipant', notable: {
        event: {
          sandpit_event: is { user.in_sandpit? },
          questionnaires: {
            id: is_in { user.rule_for_questionnaires_for_scorers }
          }
        }
      }
    end

    has_permission_on :notes, to: %i[create] do
      if_attribute notable_type: 'Event', notable: {
        sandpit_event: is { user.in_sandpit? },
        user: is { user }
      }

      if_attribute notable_type: 'Event', notable: {
        id: is_in { user.rule_for_edit_events },
        sandpit_event: is { user.in_sandpit? }
      }

      if_attribute notable_type: 'Event', notable: {
        sandpit_event: is { user.in_sandpit? },
        questionnaires: {
          id: is_in { user.rule_for_questionnaires_for_scorers }
        }
      }

      if_attribute notable_type: 'EventParticipant', notable: {
        event: {
          sandpit_event: is { user.in_sandpit? },
          user: is { user }
        }
      }

      if_attribute notable_type: 'EventParticipant', notable: {
        event: {
          sandpit_event: is { user.in_sandpit? },
          id: is_in { user.rule_for_edit_events }
        }
      }

      if_attribute notable_type: 'EventParticipant', notable: {
        event: {
          sandpit_event: is { user.in_sandpit? },
          questionnaires: { id: is_in { user.rule_for_questionnaires_for_scorers } }
        }
      }
    end

    has_permission_on :notes, to: %i[edit update] do
      if_attribute notable_type: 'Event', source: :manually_created,
                   notable: {
                     sandpit_event: is { user.in_sandpit? },
                     user: is { user }
                   }

      if_attribute notable_type: 'Event',
                   source: :manually_created,
                   notable: {
                     id: is_in { user.rule_for_edit_events },
                     sandpit_event: is { user.in_sandpit? }
                   }

      if_attribute notable_type: 'Event',
                   source: :manually_created,
                   notable: {
                     sandpit_event: is { user.in_sandpit? },
                     questionnaires: {
                       id: is_in { user.rule_for_questionnaires_for_scorers }
                     }
                   }

      if_attribute notable_type: 'EventParticipant',
                   source: is_in { %i[manually_created supplier_rejection] },
                   notable: {
                     event: {
                       sandpit_event: is { user.in_sandpit? },
                       user: is { user }
                     }
                   }

      if_attribute notable_type: 'EventParticipant',
                   source: is_in { %i[manually_created supplier_rejection] },
                   notable: {
                     event: {
                       sandpit_event: is { user.in_sandpit? },
                       id: is_in { user.rule_for_edit_events }
                     }
                   }

      if_attribute notable_type: 'EventParticipant',
                   source: is_in { %i[manually_created supplier_rejection] },
                   notable: {
                     event: {
                       sandpit_event: is { user.in_sandpit? },
                       questionnaires: { id: is_in { user.rule_for_questionnaires_for_scorers } }
                     }
                   }
    end

    has_permission_on :notes, to: %i[destroy] do
      if_attribute notable_type: 'Event', source: :manually_created,
                   notable: {
                     sandpit_event: is { user.in_sandpit? },
                     user: is { user }
                   }

      if_attribute notable_type: 'Event',
                   source: :manually_created,
                   notable: {
                     id: is_in { user.rule_for_edit_events },
                     sandpit_event: is { user.in_sandpit? }
                   }

      if_attribute notable_type: 'Event',
                   source: :manually_created,
                   notable: {
                     sandpit_event: is { user.in_sandpit? },
                     questionnaires: {
                       id: is_in { user.rule_for_questionnaires_for_scorers }
                     }
                   }

      if_attribute notable_type: 'EventParticipant',
                   source: :manually_created,
                   notable: {
                     event: {
                       sandpit_event: is { user.in_sandpit? },
                       user: is { user }
                     }
                   }

      if_attribute notable_type: 'EventParticipant',
                   source: :manually_created,
                   notable: {
                     event: {
                       sandpit_event: is { user.in_sandpit? },
                       id: is_in { user.rule_for_edit_events }
                     }
                   }

      if_attribute notable_type: 'EventParticipant',
                   source: :manually_created,
                   notable: {
                     event: {
                       sandpit_event: is { user.in_sandpit? },
                       questionnaires: { id: is_in { user.rule_for_questionnaires_for_scorers } }
                     }
                   }
    end

    has_permission_on :credits, to: %i[read create]

    has_permission_on :payments, to: %i[new update]

    # POSSIBLE BUG
    has_permission_on :payments, to: [:return] do
      if_attribute event: { user: is { user } }, sandpit_event: is { user.in_sandpit? }
    end

    has_permission_on(
      :bids,
      to: %i[
        new
        create
        edit
        update
        update_bid_notification
        upload
        cancel
        update_range update_bid_range
        real_time_update
        real_time_update_for_delete
      ]
    ) do
      if_attribute event: {
        user: is { user },
        sandpit_event: is { user.in_sandpit? }
      }

      if_attribute event: {
        id: is_in { user.rule_for_edit_events },
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on :bids, to: [:destroy] do
      if_attribute timed_out_bid: is_not { true },
                   event: {
                     user: is { user.permit_to_destroy_bid_or_event_participant },
                     sandpit_event: is { user.in_sandpit? },
                     bids_sealed?: false
                   }

      # allowed when event is sealed event and feature switch is enabled
      if_attribute timed_out_bid: is_not { true },
                   event: {
                     user: is { user.permit_to_delete_bid_on_sealed_event },
                     sandpit_event: is { user.in_sandpit? },
                     bids_sealed?: true
                   }

      # allowed from the admin permissions section
      if_attribute timed_out_bid: is_not { true },
                   event: {
                     id: is_in { user.rule_for_destroy_bids_or_event_participant },
                     sandpit_event: is { user.in_sandpit? },
                     bids_sealed?: false
                   }

      if_attribute timed_out_bid: is_not { true },
                   event: {
                     id: is_in { user.rule_for_delete_bid_on_sealed_event },
                     sandpit_event: is { user.in_sandpit? },
                     bids_sealed?: true
                   }
    end

    has_permission_on :bids, to: [:read] do
      if_attribute timed_out_bid: is_not { true },
                   event: {
                     sandpit_event: is { user.in_sandpit? },
                     bids_sealed?: false,
                     user: is { user }
                   }

      # allowed from the admin permissions section
      if_attribute timed_out_bid: is_not { true },
                   event: {
                     id: is_in { user.rule_for_show_events },
                     sandpit_event: is { user.in_sandpit? },
                     bids_sealed?: false
                   }
    end

    has_permission_on :bid_line_item_components, to: [:index] do
      if_permitted_to :read, :bid
    end

    has_permission_on(
      :bid_line_item_components,
      to: %i[
        new
        create
        edit
        update
        read
        destroy_line_item_blics
      ],
      join_by: :and
    ) do
      if_permitted_to :edit, :bid

      if_attribute line_item_component: {
        lot_component: {
          provider_cd: [1, 2]
        }
      }
    end

    has_permission_on :unit_sets, to: %i[show new]

    has_permission_on :unit_sets, to: %i[edit create update] do
      if_attribute global: false,
                   resource: is { user || user.company }
    end

    has_permission_on :unit_of_measures, to: %i[read new convert]

    has_permission_on :unit_of_measures, to: %i[edit create update] do
      if_attribute global: false,
                   resource: {
                     id: is { user.id || user.company_id }
                   }
    end

    has_permission_on :user_document_downloads, to: [:index] do
      if_attribute sandpit_event: is { user.in_sandpit? },
                   event: {
                     user: is { user }
                   }
    end

    has_permission_on :user_innovation_topic_document_downloads, to: [:index] do
      if_attribute innovation_topic: { user: is { user } }
    end

    has_permission_on :document_templates, :sim_dojo_document_templates, to: [:new]

    has_permission_on(
      :document_templates,
      :sim_dojo_document_templates,
      to: %i[
        show
        index
        edit
        create
        destroy
        update
        rule
        export_rules
      ]
    ) do
      if_attribute company: is { user.company }
    end

    has_permission_on :questionnaire_templates, to: %i[destroy update rule export_rules] do
      if_attribute company_id: is { user.rule_for_edit_questionnaire_library }
    end

    has_permission_on :event_templates, to: %i[destroy update rule export_rules] do
      if_attribute company: is { user.company }
    end

    has_permission_on :versioned_files, to: %i[index new]

    has_permission_on(
      :versioned_files,
      to: %i[
        edit
        update
        create
        destroy
        content
        new_content
        add_content
        remove_content
      ]
    ) do
      if_attribute company: is { user.company }

      if_attribute user: is { user }
    end

    # TODO: Maybe restrict this a bit? @Xymist - 2016-09-23
    has_permission_on :picklists, to: %i[show new edit create update load_picklist_options]

    has_permission_on :picklist_options, to: %i[read new edit create update]

    has_permission_on(
      :groups,
      :category_dojo_groups,
      :sim_dojo_groups,
      to: %i[
        index
        new
        show
        create
        group_participants
        participant_database
        remove_from_participant_database
        create_group_participants
        group_hosts
        create_group_hosts
        upload
      ]
    ) do
      if_attribute company: is { user.company }
    end

    has_permission_on(
      :groups,
      :category_dojo_groups,
      :sim_dojo_groups,
      to: %i[destroy edit update cancel_edit]
    ) do
      if_attribute default_group: false, deactivated_group: false,
                   company: is { user.company }
    end

    # sim_dojo event's questionnires and its components permission.
    has_permission_on :sim_dojo_events, to: %i[new create]

    has_permission_on :sim_dojo_events, to: %i[show index] do
      if_attribute user: is { user }

      # allowed from the admin permissions section
      if_attribute current_state: %w[new edit current closed],
                   id: is { user.rule_for_show_sim_dojo_event }

      if_attribute current_state: %w[edit current closed],
                   id: is_in { user.rule_for_show_scoreable_sim_dojo_events }

      if_attribute current_state: %w[edit current closed],
                   id: is_in { user.rule_for_show_approver_sim_dojo_events(@object) }

      if_attribute current_state: %w[new edit current closed],
                   id: is { user.rules_for_view_any_participant(@object) }
    end

    has_permission_on :sim_dojo_events, to: [:change_state] do
      if_attribute current_state: %w[new edit current],
                   user: is { user }

      # allowed from the admin permissions section
      if_attribute current_state: %w[new edit current],
                   id: is { user.rule_for_edit_sim_dojo_event }
    end

    has_permission_on :sim_dojo_events, to: %i[edit update] do
      if_attribute current_state: %w[new edit],
                   user: is { user }

      # allowed from the admin permissions section
      if_attribute current_state: %w[new edit],
                   id: is { user.rule_for_edit_sim_dojo_event }
    end

    has_permission_on :sim_dojo_events, to: [:close_event] do
      if_attribute current_state: ['current'],
                   user: is { user }

      # allowed from the admin permissions section
      if_attribute current_state: ['current'],
                   id: is { user.rule_for_edit_sim_dojo_event }
    end

    has_permission_on :sim_dojo_events, to: [:re_open_event] do
      if_attribute current_state: ['closed'],
                   user: is { user }

      # allowed from the admin permissions section
      if_attribute current_state: ['closed'],
                   id: is { user.rule_for_edit_sim_dojo_event }
    end

    has_permission_on :sim_dojo_documents, to: %i[arrange read sort zipped_documents] do
      # we have called the method permitted_to_show_document which check
      # with object and identify that we have to check permission for event
      # or we have to check permission for participant.
      # we have initialized object with uploaded_for_participant_id within
      # controller and thus we have this parameter to check permission.
      if_attribute event: {
        id: is { user.permitted_to_show_documents(object) }
      }

      if_attribute event: {
        id: is_in { user.rule_for_events_for_scorers(object, 'SimDojo::Event') }
      }
    end

    has_permission_on :sim_dojo_documents, to: [:upload_multiple_documents_for_questionnaire_questions_answer] do
      if_attribute event: {
        current_state: %w[edit current],
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        current_state: %w[edit current],
        id: is { user.rule_for_edit_sim_dojo_event }
      }

      # we have added rule to check if we have permission on the document
      # if document uploaded within participant document tab then that document
      # have uploaded_for_participant_id which we use to check permission
      # also if documents are uploaded for proxy answer then alos we have
      # uploaded_for_participant_id.
      if_attribute uploaded_for_participant_id: is_in {
        user.rules_for_edit_sim_participants(@object.event.company.id)
      },
                   event: {
                     current_state: is_in { %w[new edit current] }
                   }
    end

    has_permission_on(
      :sim_dojo_documents,
      to: %i[new edit create update destroy create_documents_from_document_template]
    ) do
      # we have added rule to check if we have permission on the document
      # if document uploaded within participant document tab then that document
      # have uploaded_for_participant_id which we use to check permission
      if_attribute uploaded_for_participant_id: is_in {
        user.rules_for_edit_sim_participants(@object.event.company.id)
      },
                   event: {
                     current_state: is_in { %w[new edit current] }
                   }

      if_attribute uploaded_for_participant_id: is { nil },
                   event: {
                     user: is { user },
                     current_state: is_in { %w[new edit current] }
                   }

      # allowed from the admin permissions section
      if_attribute uploaded_for_participant_id: is { nil },
                   event: {
                     id: is { user.rule_for_edit_sim_dojo_event },
                     current_state: is_in { %w[new edit current] }
                   }

      if_attribute user_id: is_not { user.acting_as_id },
                   questionnaire_questions_answer_id: is_not { nil }
    end

    has_permission_on :sim_dojo_filter_views, to: %i[update create destroy] do
      if_attribute item_id: is { user.id }

      if_attribute item_id: is { user.rule_for_edit_sim_dojo_event }
    end

    has_permission_on :sim_dojo_questionnaires, to: %i[edit update destroy
                                                       weightings_check
                                                       weightings_update
                                                       create_from_template
                                                       edit_description
                                                       download_report
                                                       upload_questionnaire] do
      if_attribute event: {
        current_state: %w[new edit],
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        current_state: %w[new edit],
        id: is { user.rule_for_edit_sim_dojo_event }
      }
    end

    has_permission_on :sim_dojo_questionnaires, to: %i[show participants_answer_report] do
      if_attribute event: { user: is { user } }

      # allowed from the admin permissions section
      if_attribute event: { id: is { user.rule_for_show_sim_dojo_event } }

      if_attribute id: is_in { user.rule_for_questionnaires_for_scorers(@object.id) }
    end

    has_permission_on :sim_dojo_questionnaire_questions, to: %i[
      sort create update destroy update_display_when clone_question
    ]

    has_permission_on :sim_dojo_questionnaire_sections, to: %i[sort create
                                                               update destroy
                                                               clone_section
                                                               edit_description
                                                               from_text]

    has_permission_on :sim_dojo_workflow_selection_rules, to: %i[edit update
                                                                 new create
                                                                 update_ui]

    has_permission_on :sim_dojo_event_participants, to: %i[arrange show update_remote contracts audit_logs] do
      # allowed user with event_participant permission.
      if_attribute id: is_in { user.rules_for_show_sim_participants(@object.event.company.id) }

      if_attribute event: {
        id: is_in { user.rule_for_events_for_scorers(object, 'SimDojo::Event') }
      }

      if_attribute id: is { user.rule_for_show_approver_sim_dojo_event_participant(object) }
    end

    has_permission_on(
      :sim_dojo_event_participants,
      to: %i[select_inviting_options mass_re_invite mass_recycle select_approval_options]
    ) do
      if_attribute event: { id: is { user.rule_for_invite_participant } }
    end

    has_permission_on :sim_dojo_event_participants, to: %i[show] do
      if_attribute id: is { user.rule_for_show_escalatee_sim_dojo_event_participant(object) }
    end

    has_permission_on :sim_dojo_event_participants, to: %i[mass_re_invite mass_recycle] do
      if_attribute event: { user: is { user } }
    end

    has_permission_on :sim_dojo_event_participants, to: %i[index email_participant_list] do
      if_attribute event: { user: is { user } }

      # allowed from the admin permissions section
      if_attribute event: { id: is { user.rule_for_show_sim_dojo_event } }

      if_attribute event: { id: is_in { user.rule_for_events_for_scorers(object, 'SimDojo::Event') } }

      if_attribute id: is { user.rule_for_show_approver_sim_dojo_event_participant(object) }

      if_attribute event: { id: is { user.rules_for_view_any_participant(@object.event) } }
    end

    has_permission_on(
      :sim_dojo_event_participants,
      to: %i[new create upload create_from_group create_from_company_database participants_resub_notification]
    ) do
      # allowed from the admin permissions section
      if_attribute event: {
        current_state: %w[current edit],
        id: is { user.rule_for_invite_participant }
      }
    end

    has_permission_on(
      :sim_dojo_event_participants,
      to: %i[
        update_tags
        init_resubmit_or_ask_for_this_questionnaire
        resubmit_or_ask_further_decision
        save_nominated_scorer
        archive_questionnaire
        unlink_questionnaire
        update
        unarchive_questionnaire
        destroy
        recover_event_participants
        recover
        reinvite
        delete_all_event_participants
        create_update_custom_column
        create_update_custom_column_values
        workflow_assignment_check
        update_workflow_assignment_check
        questionnaire_auto_selection
        approval_set_auto_selection
        select_expired_document_questions
        save_nominated_approver
        save_nominated_reapprover
        trigger_push_integration
      ]
    ) do
      # allowed user with event_participant permission.
      if_attribute id: is_in { user.rules_for_edit_sim_participants(@object.event.company.id) },
                   event: {
                     current_state: %w[edit current]
                   }

      if_attribute event: {
        current_state: %w[edit current],
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is { user.rule_for_edit_sim_dojo_event },
        current_state: %w[edit current]
      }

      if_attribute event: {
        id: is { user.rule_for_invite_participant },
        current_state: %w[current edit]
      }
    end

    has_permission_on :sim_dojo_event_participants, to: %i[recover_event_participants] do
      if_attribute event: {
        id: is { user.rule_for_recover_participants(@object.event) }
      }
    end

    has_permission_on :sim_dojo_event_participants, to: %i[update_decision] do
      # allowed from the admin permissions section
      if_attribute event: {
        id: is { user.rule_for_sim_final_decisions },
        current_state: %w[edit current]
      }
    end

    has_permission_on :sim_dojo_event_participant_contacts, to: %i[index] do
      # allowed user with event_participant permission.
      if_attribute sim_dojo_event_participant: {
        id: is_in { user.rules_for_show_sim_participants(@object.event.company.id) }
      }
    end

    has_permission_on(
      :sim_dojo_event_participant_contacts,
      to: %i[new create edit update destroy invite reinvite registered_user]
    ) do
      # allowed user with event_participant permission.
      if_attribute sim_dojo_event_participant: {
        id: is_in { user.rules_for_show_sim_participants(@object.event.company.id) }
      }

      if_attribute sim_dojo_event_participant: {
        event: { id: is { user.rule_for_invite_participant } }
      }
    end

    has_permission_on :sim_dojo_questionnaire_answers, to: %i[edit update new index list_for_summary] do
      if_attribute event: { user: is { user } }

      # allowed from the admin permissions section
      if_attribute event: { id: is { user.rule_for_show_sim_dojo_event } }

      if_attribute questionnaire: {
        id: is_in { user.rule_for_questionnaires_for_scorers }
      }
      # allowed user with event_participant permission.
      if_attribute event_participant: {
        id: is_in { user.rules_for_show_sim_participants(@object.event.company.id) }
      }
    end

    has_permission_on :sim_dojo_questionnaire_answers, to: %i[export_xlsx history show] do
      # allowed from the admin permissions section
      if_attribute questionnaire: {
        id: is_in { user.rule_for_questionnaires_for_scorer_and_reapprovers(@object.questionnaire_id) }
      }

      # allowed user with event_participant permission.
      if_attribute event_participant: {
        id: is_in { user.rules_for_show_sim_participants(@object.event.company.id) }
      }

      if_attribute event_participant: {
        id: is { user.rule_for_show_approver_sim_dojo_event_participant(@object.event_participant) }
      }
    end

    has_permission_on :sim_dojo_questionnaire_answers, to: %i[update destroy update_question_answer accept decline] do
      if_attribute questionnaire: {
        event: {
          current_state: %w[edit current],
          user: is { user }
        }
      }

      # allowed from the admin permissions section
      if_attribute questionnaire: {
        id: is_in { user.rule_for_questionnaires_for_scorers(@object.questionnaire_id) },
        event: {
          current_state: %w[edit current]
        }
      }

      # allowed user with event_participant permission.
      if_attribute event_participant: {
        id: is_in { user.rules_for_show_sim_participants(@object.event.company.id) }
      }
    end

    has_permission_on :sim_dojo_questionnaire_answers, to: %i[edit update], join_by: :and do
      if_attribute participant: is { user.acting_as_user }

      # allowed from the admin permissions section
      if_attribute id: is { user.rule_for_show_sim_dojo_event }
    end

    has_permission_on :sim_dojo_questionnaire_questions_answers, to: [:update] do
      # allowed user with event_participant permission.
      if_attribute questionnaire_answer: {
        event_participant: {
          id: is_in { user.rules_for_show_sim_participants(@object.event.company.id) }
        }
      }

      if_attribute questionnaire_answer: {
        questionnaire: {
          event: {
            user: is { user }
          }
        }
      }

      if_attribute questionnaire_answer: {
        questionnaire: {
          event: {
            id: is { user.rule_for_show_sim_dojo_event }
          }
        }
      }
    end

    has_permission_on :sim_dojo_questionnaire_questions_answers, to: [:read] do
      if_attribute questionnaire_answer: {
        questionnaire: {
          event: {
            user: is { user }
          }
        }
      }

      if_attribute questionnaire_answer: {
        questionnaire: {
          event: {
            id: is { user.rule_for_show_sim_dojo_event }
          }
        }
      }

      if_attribute questionnaire_answer: {
        questionnaire: {
          id: is_in { user.rule_for_questionnaires_for_scorers(@object.questionnaire.id) }
        }
      }
    end

    has_permission_on(
      :sim_dojo_scorers,
      to: %i[
        index
        new
        create
        select_inviting_scorers_options
        create_from_group
        create_scorer_from_company_database
        update_scorer_assignments
        add_scorer_box
        destroy
      ]
    ) do
      if_attribute scoreable: { event_type: 'SimDojo::Event' }

      if_permitted_to :edit, :scoreable
    end

    has_permission_on :sim_dojo_decision, to: %i[new create update edit escalate_approval] do
      if_attribute event_participant: {
        event: {
          current_state: %w[edit current],
          user: is { user }
        }
      }

      # allowed from the admin permissions section
      if_attribute event_participant: {
        event: {
          id: is { user.rule_for_show_sim_dojo_event },
          current_state: %w[edit current]
        }
      }

      if_attribute event_participant: {
        event: {
          id: is_in { user.rule_for_show_approver_sim_dojo_events(@object.event_participant.event) }
        }
      }

      # allowed user with event_participant permission.
      if_attribute event_participant: {
        id: is_in { user.rules_for_show_sim_participants(@object.event_participant.event.company.id) },
        event: {
          current_state: %w[edit current]
        }
      }
    end

    has_permission_on :sim_dojo_decision, to: %i[new create] do
      if_attribute event_participant: {
        id: is { user.rule_for_show_escalatee_sim_dojo_event_participant(object.event_participant) }
      }
    end

    has_permission_on :sim_dojo_messages, to: %i[read new create] do
      # allowed user with event_participant permission.
      if_attribute sim_dojo_event_participant_id: is_in {
        user.rules_for_show_sim_participants(@object.sim_dojo_event_participant.event.company.id)
      }

      if_attribute message_subject_type: 'SimDojo::Event',
                   message_subject: {
                     current_state: %w[edit current],
                     user: is { user }
                   }
    end

    has_permission_on :questionnaires, :sim_dojo_questionnaires, to: [:save_as_template] do
      if_attribute company_id: is { user.rule_for_edit_questionnaire_library }
    end

    has_permission_on :questionnaire_templates, :sim_dojo_questionnaire_templates, to: %i[destroy update] do
      if_attribute company_id: is { user.rule_for_edit_questionnaire_library }
    end

    has_permission_on :questionnaire_templates, :sim_dojo_questionnaire_templates, to: [:index] do
      if_attribute company_id: is { user.rule_for_view_questionnaire_library }
    end

    has_permission_on :questionnaire_templates, to: %i[destroy update rule] do
      if_attribute company_id: is { user.rule_for_edit_questionnaire_library }
    end

    has_permission_on(
      :scorers,
      to: %i[
        index
        new
        create
        select_inviting_scorers_options
        create_from_group
        create_scorer_from_company_database
        update_scorer_assignments
        add_scorer_box
        destroy
      ]
    ) do
      if_permitted_to :edit, :scoreable
    end

    has_permission_on :lot_templates, to: %i[index list]

    has_permission_on :lot_templates, to: %i[destroy update create new rule export_rules] do
      if_attribute company_id: is { user.rule_for_edit_lot_library }
    end

    has_permission_on :questionnaires, to: %i[index new]

    # we let the "edit" accessible for their host and redirect to "show" in all states
    # because it would be too brutal to eject to "permission denied page" if you accidentally lend on it.
    has_permission_on :questionnaires, to: [:show] do
      if_attribute event: {
        sandpit_event: is { user.in_sandpit? },
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_show_events },
        sandpit_event: is { user.in_sandpit? }
      }

      if_attribute id: is_in { user.rule_for_questionnaires_for_scorers(@object.id) }
    end

    has_permission_on(
      :questionnaires,
      to: %i[
        edit
        create_from_template
        edit_description
        download_report
        upload_questionnaire
      ]
    ) do
      if_attribute event: {
        sandpit_event: is { user.in_sandpit? },
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_edit_events },
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on :questionnaires, to: %i[update destroy weightings_check weightings_update] do
      if_attribute event: {
        current_state: %w[new edit],
        sandpit_event: is { user.in_sandpit? },
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is_in { user.rule_for_show_events },
        current_state: %w[new edit],
        sandpit_event: is { user.in_sandpit? }
      }
    end

    has_permission_on(
      :questionnaire_sections,
      to: %i[
        sort
        create
        update
        destroy
        clone_section
        edit_description
        from_text
      ]
    ) do
      if_permitted_to :edit, :questionnaire
    end

    has_permission_on :questionnaire_sections, to: [:read] do
      if_permitted_to :read, :questionnaire
    end

    has_permission_on(
      :questionnaire_questions,
      to: %i[
        sort
        create
        update
        destroy
        update_display_when
        clone_question
      ]
    ) do
      if_permitted_to :edit, :questionnaire
    end

    has_permission_on :questionnaire_questions, to: [:read] do
      if_permitted_to :read, :questionnaire
    end

    has_permission_on :workflow_selection_rules, to: %i[edit update new create] do
      if_permitted_to :edit, :questionnaire
    end

    has_permission_on(
      :questionnaire_answers,
      to: %i[
        edit
        update
        index
        new
        list_for_summary
        upload_questionnaire_response
      ]
    ) do
      if_attribute event: {
        sandpit_event: is { user.in_sandpit? },
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        sandpit_event: is { user.in_sandpit? },
        id: is_in { user.rule_for_show_events }
      }

      if_attribute questionnaire: { id: is_in { user.rule_for_questionnaires_for_scorers(@object.questionnaire_id) } }
    end

    has_permission_on :questionnaire_questions_answers, to: [:update]

    has_permission_on :questionnaire_answers, to: %i[export_xlsx show] do
      # allowed from the admin permissions section
      if_attribute questionnaire: {
        id: is_in { user.rule_for_questionnaires_for_scorers(@object.questionnaire_id) },
        passed_deadline?: true,
        event: {
          sealed_functionality: true
        }
      }

      if_attribute questionnaire: {
        id: is_in { user.rule_for_questionnaires_for_scorers(@object.questionnaire_id) },
        event: {
          sealed_functionality: false
        }
      }

      if_attribute questionnaire: {
        event: {
          sealed_functionality: false,
          sandpit_event: is { user.in_sandpit? },
          user: is { user }
        }
      }

      if_attribute questionnaire: {
        event: {
          id: is_in { user.rule_for_show_events },
          sealed_functionality: false,
          sandpit_event: is { user.in_sandpit? }
        }
      }

      if_attribute questionnaire: {
        passed_deadline?: true,
        event: {
          sealed_functionality: true,
          sandpit_event: is { user.in_sandpit? },
          user: is { user }
        }
      }

      if_attribute questionnaire: {
        passed_deadline?: true,
        event: {
          id: is_in { user.rule_for_show_events },
          sealed_functionality: true,
          sandpit_event: is { user.in_sandpit? }
        }
      }
    end

    has_permission_on(
      :questionnaire_answers,
      to: %i[
        update
        destroy
        update_question_answer
        accept
        decline
        undo_decision
      ]
    ) do
      if_attribute questionnaire: { id: is_in { user.rule_for_questionnaires_for_scorers(@object.questionnaire_id) } }

      if_attribute questionnaire: {
        event: {
          sandpit_event: is { user.in_sandpit? },
          user: is { user }
        }
      }

      # allowed from the admin permissions section
      if_attribute questionnaire: {
        event: {
          id: is_in { user.rule_for_edit_events },
          sandpit_event: is { user.in_sandpit? }
        }
      }
    end

    has_permission_on :innovation_topics, to: %i[new create] do
      if_attribute user: is { user.rule_for_innovation_topics }
    end

    has_permission_on :innovation_topics, to: %i[read edit update destroy show] do
      if_attribute user: is { user }
    end

    has_permission_on :innovation_topics, to: [:return_to_editing] do
      if_attribute current_state: %w[new pending edit running completed],
                   user: is { user }
    end

    has_permission_on :innovation_topics, to: %i[close_topic re_open_topic] do
      if_attribute current_state: %w[completed closed],
                   user: is { user }
    end

    has_permission_on :innovation_topic_documents, to: %i[index new] do
      if_permitted_to :read, :innovation_topic
    end

    has_permission_on :innovation_topic_documents, to: %i[arrange read] do
      if_attribute innovation_topic: { user: is { user } }
    end

    has_permission_on(
      :innovation_topic_documents,
      to: %i[edit create update destroy create_documents_from_document_template]
    ) do
      if_attribute innovation_topic: {
        current_state: %w[new edit],
        user: is { user }
      }
    end

    has_permission_on(
      :innovation_topic_participants,
      to: %i[arrange read accept reject update_remote new select_inviting_options]
    ) do
      if_attribute innovation_topic: { user: is { user } }
    end

    has_permission_on(
      :innovation_topic_participants,
      to: %i[edit create update destroy upload create_from_group create_from_company_database]
    ) do
      if_attribute innovation_topic: {
        current_state: %w[new edit],
        user: is { user }
      }
    end

    has_permission_on :category_dojo_portfolios, to: %i[new create edit update clone summary]

    has_permission_on :category_dojo_categories, to: %i[new create edit update]

    has_permission_on :innovation_topic_ideas, to: %i[read score update] do
      if_attribute innovation_topic: { user: is { user } }
    end

    has_permission_on :authorization_rules, to: :read

    has_permission_on :approval_sets, to: %i[edit new create update destroy change_assign_to_uninvited_suppliers] do
      if_attribute event: {
        current_state: %w[edit],
        user: is { user }
      }

      # allowed from the admin permissions section
      if_attribute event: {
        id: is { user.rule_for_edit_sim_dojo_event },
        current_state: %w[edit new]
      }
    end

    has_permission_on :approval_sets, to: %i[index show] do
      if_attribute event: { user: is { user } }

      if_attribute event: { id: is { user.rule_for_show_sim_dojo_event } }
    end

    has_permission_on :approval_steps, to: %i[create sort_step destroy] do
      if_attribute approval_set: {
        event: {
          id: is { user.rule_for_edit_sim_dojo_event },
          current_state: %w[edit new]
        }
      }
    end

    has_permission_on :approvals, to: %i[create sort_approval destroy update escalation_list save_escalation_list] do
      if_attribute approval_step: {
        approval_set: {
          event: {
            id: is { user.rule_for_edit_sim_dojo_event },
            current_state: %w[edit new]
          }
        }
      }
    end

    has_permission_on(
      :contract_configurations,
      to: %i[
        index
        update_contract_column
        create_contract_document_type
        destroy_contract_document_type
      ]
    )

    has_permission_on :contracts, to: %i[index new filter_contracts change_contracts_owner]

    has_permission_on :contracts, to: %i[show download download_template] do
      if_attribute user: is { user }

      if_attribute id: is_in { user.rule_for_show_contracts }

      if_attribute id: is_in { user.rule_for_edit_contracts }

      if_attribute id: is { user.added_as_stakeholder?(@object) }
    end

    has_permission_on :contracts, to: %i[add_notes download_file_document
                                         update_note] do
      if_attribute user: is { user }

      if_attribute id: is_in { user.rule_for_show_contracts }

      if_attribute id: is_in { user.rule_for_edit_contracts }

      if_attribute id: is { user.added_as_stakeholder?(@object) }
    end

    has_permission_on(
      :contracts,
      to: %i[
        edit
        update
        mark_archive
        add_contact
        add_stakeholder
        remove_user
        add_file
        remove_file
        sort
        add_category
        add_label
        upload
        remove_relation
        add_relation
        create_update_custom_column
        create_update_custom_column_values
        add_or_update_contract_notification
        audit_trail
      ]
    ) do
      if_attribute user: is { user }

      if_attribute id: is_in { user.rule_for_edit_contracts }
    end
    # User with edit permission on contract can update versioned file.
    # From contract we are updating metadata and storing physical location.
    # Permission for versioned file already defined but we have to define it
    # again to make it working with contracts.
    has_permission_on :versioned_files, to: [:update] do
      if_permitted_to :edit, :contracts
    end

    has_permission_on :custom_columns, to: %i[index show]

    has_permission_on :supplier_discovery, to: %i[search list_locations list_industries company_details] do
      if_attribute id: is_in { user.rule_for_edit_events + Event.where(user:).pluck(:id) }
    end

    has_permission_on :event_collaborators, to: [:index] do
      if_attribute event: { user: is { user } }
    end
  end

  role :Participant do
    has_permission_on :companies, to: %i[logo favicon]

    has_permission_on(
      :users,
      to: %i[
        show
        edit
        update
        home
        sourcing_dashboard
        change_password
        change_password_update
        update_dashboard_configuration
        load_more_messages
        forgot_password
        reset_password
        calendar
      ]
    ) do
      if_attribute id: is { user.id }
    end

    has_permission_on :resources, to: [:show]

    has_permission_on :events, to: %i[invitations mark_as_flash_message
                                      public_invitations apply_and_accept_invitation]

    has_permission_on :events, to: [:invited] do
      # TODO: find a more elegant way to check for either null or false...
      if_attribute current_state: %w[running pending edit rfq post_rfq_pre_auction pause],
                   sandpit_event: is { user.in_sandpit? },
                   event_participants: {
                     participating: nil,
                     disable_access: nil,
                     user: is { user }
                   }

      if_attribute current_state: %w[pending edit rfq post_rfq_pre_auction pause],
                   sandpit_event: is { user.in_sandpit? },
                   event_participants: {
                     participating: nil,
                     disable_access: false,
                     user: is { user }
                   }

      # For sandpit events, we only want to show the events that the Host owns when he pretends to be a participant
      if_attribute current_state: %w[running pending edit rfq post_rfq_pre_auction pause],
                   sandpit_event: is { user.in_sandpit? },
                   user: is { user },
                   event_participants: {
                     user_id: is { user.acting_as_id },
                     participating: nil,
                     disable_access: nil
                   }

      if_attribute current_state: %w[pending edit rfq post_rfq_pre_auction pause],
                   sandpit_event: is { user.in_sandpit? },
                   user: is { user },
                   event_participants: {
                     user_id: is { user.acting_as_id },
                     participating: nil,
                     disable_access: false
                   }
    end

    has_permission_on :events, to: :participate do
      if_attribute sandpit_event: is { user.in_sandpit? },
                   event_participants: {
                     user_id: is { user.acting_as_id },
                     participating: true,
                     accepted: true
                   }
    end

    # >----------------------
    # Delegating to CanCanCan
    # >----------------------
    #
    has_permission_on :events, to: %i[read event_exchange_rate] do
      if_attribute id: is { @object.is_a?(Event) && user.abilities.can?(:show, @object) ? @object.id : nil }

      if_attribute id: is_in { @object.blank? ? Event.accessible_by(user.abilities, :show).select(:id) : [] }
    end

    # IMPORTANT:
    #      The :index rule deliberately sets empty conditions on `event_participants` when fetching a collection - purely to force a left join.
    #      This join previously occurred when we actually filtered on `event_participants` at this level, and multiple
    #      bits of downstream code have an invisible dependency on this join having already been performed.
    #
    #      This is expensive, and we should remove it when all such downstream code has been identified, and changed
    #      to either do its own join, or work without one.
    has_permission_on :events, to: [:index] do
      if_attribute id: is { @object.is_a?(Event) && user.abilities.can?(:index, @object) ? @object.id : nil }

      if_attribute event_participants: {},
                   id: is_in { @object.blank? ? Event.accessible_by(user.abilities, :index).select(:id) : [] }
    end
    # >----------------------
    # >----------------------

    # SIM Dojo rules
    has_permission_on :sim_dojo_event_participants, to: [:read] do
      if_attribute id: is_in { user.rule_for_show_sim_dojo_event_participant_contacts(@object.event_id) },
                   participating: true,
                   accepted: true,
                   event: {
                     current_state: %w[edit current]
                   }
    end

    has_permission_on :sim_dojo_events, to: [:index] do
      if_attribute event_participants: { user_id: is { user.id } }
    end

    has_permission_on :sim_dojo_event_participants, to:
      %i[accept_invite onboard_uninvited_supplier reject_invite]

    has_permission_on :sim_dojo_event_participants, to: %i[show edit] do
      if_attribute invited: [true],
                   accepted: [true],
                   user: is { user }
    end

    has_permission_on :sim_dojo_event_participant_contacts, to: %i[index] do
      if_attribute sim_dojo_event_participant: {
        id: is_in do
          user.rule_for_show_sim_dojo_event_participant_contacts(@object.sim_dojo_event_participant.event.id)
        end,
        participating: true,
        accepted: true
      }
    end

    has_permission_on :sim_dojo_event_participant_contacts, to: %i[destroy] do
      if_attribute sim_dojo_event_participant: {
        id: is_in do
          user.rule_for_edit_sim_dojo_event_participant_contacts(@object.sim_dojo_event_participant.event.id)
        end,
        participating: true,
        accepted: true
      }
    end
    # Participant needs to send host a message, even if they have not been granted access to the event
    has_permission_on :sim_dojo_messages, to: %i[new create]

    # Possibly dead code? `current_state` is not a valid attribute for `sim_dojo_messages`
    has_permission_on :sim_dojo_messages, to: %i[index update_message] do
      if_attribute message_subject: {
        event_participants: {
          id: is_in { user.rule_for_show_sim_dojo_event_participant_contacts(@object.message_subject_id) },
          participating: true,
          accepted: true
        },
        current_state: %w[edit current]
      }
    end

    has_permission_on :sim_dojo_messages, to: [:read] do
      if_attribute message_recipients: { user_id: is { user.acting_as_id } },
                   message_subject_type: 'SimDojo::Event'

      if_attribute user_id: is { user.acting_as_id },
                   message_subject_type: 'SimDojo::Event'
    end

    has_permission_on :sim_dojo_questionnaire_answers, to: [:export_xlsx] do
      if_attribute event: {
        current_state: %w[edit current],
        event_participants: {
          id: is_in { user.rule_for_show_sim_dojo_event_participant_contacts(@object.event.id) },
          participating: true,
          accepted: true
        }
      }
    end

    has_permission_on :sim_dojo_questionnaire_answers, to: [:index] do
      if_attribute event_participant: { participating: true, accepted: true }
    end

    has_permission_on :sim_dojo_questionnaire_answers, to: [:show], join_by: :and do
      if_attribute event_participant: {
        id: is_in { user.rule_for_show_sim_dojo_event_participant_contacts(@object.event_id) },
        participating: true,
        accepted: true
      }

      if_attribute event: {
        current_state: %w[edit current]
      }
    end

    has_permission_on :sim_dojo_questionnaire_answers, to: %i[edit update], join_by: :and do
      if_attribute event_participant: {
        id: is_in { user.rule_for_edit_sim_dojo_event_participant_contacts(@object.event_id) },
        participating: true,
        accepted: true
      }

      if_attribute event: {
        current_state: %w[edit current]
      }
    end

    has_permission_on :sim_dojo_documents, to: [:destroy], join_by: :and do
      if_attribute event: {
        event_participants: {
          id: is_in { user.rule_for_show_sim_dojo_event_participant_contacts(@object.documentable_id) },
          participating: true,
          accepted: true
        }
      }

      if_attribute questionnaire_questions_answer_id: is_not { nil }
    end

    has_permission_on :sim_dojo_documents, to: [:index], join_by: :and do
      if_attribute event: {
        current_state: %w[edit current],
        event_participants: {
          id: is_in { user.rule_for_show_sim_dojo_event_participant_contacts(@object.documentable_id) },
          participating: true,
          accepted: true
        }
      }

      if_attribute event: {
        id: is { @object.permission_for_show_document_tab_in_sim }
      }
    end

    has_permission_on :sim_dojo_documents, to: %i[upload_multiple_documents_for_questionnaire_questions_answer] do
      if_attribute event: {
        current_state: %w[edit current],
        event_participants: {
          id: is_in { user.rule_for_edit_sim_dojo_event_participant_contacts(@object.documentable_id) },
          participating: true,
          accepted: true
        }
      }
    end

    has_permission_on :sim_dojo_documents, to: [:show], join_by: :and do
      if_attribute event: {
        current_state: %w[edit current],
        event_participants: {
          id: is_in { user.rule_for_show_sim_dojo_event_participant_contacts(@object.documentable_id) },
          participating: true,
          accepted: true
        }
      }

      if_attribute id: is { @object.with_permission_to_sim_document(user) }
    end

    has_permission_on :surveys, to: %i[new create] do
      if_permitted_to :read, :survey_subject

      if_attribute survey_subject: {
        current_state: %w[edit current],
        event_participants: {
          id: is_in { user.rule_for_edit_sim_dojo_event_participant_contacts(@object.survey_subject.id) },
          participating: true,
          accepted: true
        }
      }
    end

    has_permission_on :event_participants, to: [:mark_survey_offered] do
      if_permitted_to :read, :event
    end

    has_permission_on :documents, to: %i[new create] do
      if_attribute user: is { user }
      if_permitted_to :index, :event
    end

    has_permission_on :documents, to: [:destroy] do
      if_attribute user_id: is { user.acting_as_id },
                   questionnaire_questions_answer_id: is_not { nil }
    end

    has_permission_on(
      :documents,
      to: %i[
        index
        zipped_documents
        upload_multiple_documents_for_questionnaire_questions_answer
      ]
    ) do
      if_permitted_to :read, :event
    end

    has_permission_on :lots, to: %i[read monitor monitor_status] do
      if_permitted_to :read, :event
    end

    has_permission_on :event_rounds, to: %i[show] do
      if_permitted_to :read, :event
    end

    has_permission_on :documents, to: [:index] do
      if_permitted_to :read, :event
    end

    has_permission_on :documents, to: [:show], join_by: :and do
      if_permitted_to :show, :event

      if_attribute id: is { @object.with_permission(user, true) }
    end

    has_permission_on :event_participants, to: %i[accept_invite reject_invite] do
      if_attribute user: is { user }

      if_attribute user: is { user.acting_as_user }, event: { user: is { user } }
    end

    has_permission_on :event_participants, to: %i[show edit] do
      if_attribute user: is { user }, invited: [true], accepted: [true]
    end

    has_permission_on :bids, to: %i[new upload update_range update_bid_range] do
      if_permitted_to :participate, :event
    end

    has_permission_on :bids, to: %i[create cancel real_time_update real_time_update_for_delete], join_by: :and do
      if_attribute user: is { user.acting_as_user }

      if_attribute user: { accepted_t_and_c: true }

      if_permitted_to :participate, :event
    end

    has_permission_on :bids, to: %i[edit update], join_by: :and do
      if_attribute user: is { user.acting_as_user }

      if_attribute user: { accepted_t_and_c: true }

      if_attribute lot: { complex_structure: true }

      if_permitted_to :participate, :event
    end

    has_permission_on :bid_line_item_components, to: %i[new create edit update destroy_line_item_blics],
                                                 join_by: :and do
      if_permitted_to :edit, :bid

      if_attribute line_item_component: {
        lot_component: { provider_cd: [1, 2] }
      }
    end

    has_permission_on :bids, to: [:show] do
      if_attribute user: is { user.acting_as_user }
    end

    has_permission_on :questionnaires, to: %i[index show] do
      if_permitted_to :read, :event
    end

    has_permission_on :questionnaire_answers, to: %i[export_xlsx show upload_questionnaire_response], join_by: :and do
      if_attribute participant: is { user.acting_as_user }

      if_attribute event: {
        event_participants: {
          user_id: is { user.id },
          participating: true,
          accepted: true
        }
      }
      if_permitted_to :read, :event
    end

    has_permission_on :questionnaire_answers, to: %i[export_xlsx show upload_questionnaire_response] do
      if_attribute id: is {
        @object.is_a?(QuestionnaireAnswer) && user.abilities.can?(:show, @object) ? @object.id : nil
      }
    end

    has_permission_on :questionnaire_answers, to: [:index] do
      if_permitted_to :read, :event
    end

    has_permission_on :questionnaire_answers, to: [:index], join_by: :and do
      if_permitted_to :read, :event
    end

    has_permission_on :questionnaire_answers, to: %i[edit update] do
      if_attribute id: is {
        if @object.is_a?(QuestionnaireAnswer)
          # Allow edit action if user can edit OR can view (controller will handle redirect to show)
          user.abilities.can?(:edit, @object) || user.abilities.can?(:show, @object) ? @object.id : nil
        end
      }
    end

    has_permission_on :questionnaire_answers, to: %i[edit update], join_by: :and do
      if_attribute participant: is { user }

      if_attribute questionnaire: { passed_deadline?: false }

      if_permitted_to :participate, :event

      if_attribute event: {
        sandpit_event: true,
        user: is { user }
      }

      if_attribute event: is { user.rule_for_show_sim_dojo_event }
    end

    # Sandpit events
    has_permission_on :questionnaire_answers, to: %i[show upload_questionnaire_response], join_by: :and do
      if_attribute participant: is { user.acting_as_user }

      if_attribute event: {
        sandpit_event: true,
        user: is { user }
      }
      if_permitted_to :participate, :event
    end

    has_permission_on :innovation_topic_participants, to: %i[accept_invite reject_invite]

    has_permission_on :innovation_topic_participants, to: %i[show edit] do
      if_attribute invited: [true],
                   accepted: [true],
                   user: is { user }
    end

    has_permission_on :innovation_topics, to: [:invitations]

    has_permission_on :innovation_topics, to: [:invited] do
      if_attribute innovation_topic_participants: { user: is { user } }
    end

    has_permission_on :innovation_topics, to: :participate do
      if_attribute innovation_topic_participants: {
        participating: true,
        accepted: true,
        user: is { user }
      }
    end

    has_permission_on :innovation_topics, to: [:read] do
      if_attribute user: is { user }

      if_attribute current_state: %w[new pending edit running completed],
                   innovation_topic_participants: {
                     accepted: true,
                     participating: true,
                     user: is { user }
                   }
    end

    has_permission_on :innovation_topics, to: [:index] do
      if_attribute innovation_topic_participants: {
        accepted: true,
        user: is { user }
      }
    end

    has_permission_on :innovation_topics, to: %i[new create] do
      if_attribute user: is { user.rule_for_innovation_topics }
    end

    has_permission_on :innovation_topics, to: %i[edit update destroy transfer_host] do
      if_attribute user: is { user }
    end

    has_permission_on :innovation_topic_documents, to: %i[new create]

    has_permission_on :innovation_topic_documents, to: [:read] do
      if_attribute innovation_topic: {
        user: is { user }
      }
      if_permitted_to :read, :innovation_topic
    end

    has_permission_on :messages, to: %i[index messages_greater_than update_message] do
      if_permitted_to :read, :message_subject
    end

    has_permission_on :messages, to: %i[messages_greater_than] do
      if_attribute id: is { 0 }
    end

    # Participant needs to send host a message, even if they have not been granted access to the event
    has_permission_on :messages, to: %i[new create]

    has_permission_on :messages, to: [:read] do
      if_attribute message_subject_type: is_in { %w[InnovationTopic Event] },
                   message_recipients: {
                     user_id: is { user.acting_as_id }
                   },
                   message_subject: {
                     sandpit_event: false
                   }

      if_attribute user_id: is { user.acting_as_id },
                   message_subject_type: is_in { %w[InnovationTopic Event] },
                   message_subject: {
                     sandpit_event: false
                   }

      # sandpit events, if impersonating participant, and this user owns it...
      if_attribute message_subject_type: 'Event',
                   message_recipients: {
                     user_id: is { user.acting_as_id }
                   },
                   message_subject: {
                     sandpit_event: true,
                     user: is { user }
                   }

      if_attribute user_id: is { user.acting_as_id },
                   message_subject_type: 'Event',
                   message_subject: {
                     sandpit_event: true,
                     user: is { user }
                   }
    end

    has_permission_on :innovation_topic_ideas, to: %i[new create edit update] do
      if_attribute innovation_topic: { user: is { user } }

      if_permitted_to :read, :innovation_topic
    end

    has_permission_on :innovation_topic_ideas, to: [:read] do
      if_attribute innovation_topic: { user: is { user } }

      if_permitted_to :read, :innovation_topic
    end

    has_permission_on :unit_of_measures, to: [:convert]

    has_permission_on :innovation_topic_documents, to: %i[new create]

    has_permission_on :questionnaire_questions_answers, to: [:update] do
      if_permitted_to :edit, :questionnaire_answer

      if_permitted_to :edit, :event
    end

    has_permission_on :sim_dojo_questionnaire_questions_answers, to: [:update] do
      if_attribute event: {
        current_state: %w[edit current],
        event_participants: {
          id: is_in { user.rule_for_edit_sim_dojo_event_participant_contacts(@object.event.id) },
          participating: true,
          accepted: true
        }
      }
    end

    has_permission_on :event_collaborators, to: [:index] do
      if_permitted_to :read, :event
    end
  end

  role :guest do
    has_permission_on(
      %i[users category_dojo_users],
      to: %i[new create forgot_password reset_password unlink_from_crm current_opportunities]
    )

    has_permission_on :companies, to: %i[logo favicon]

    has_permission_on :payments, to: [:notify]

    has_permission_on :users, to: %i[auth_headers redirect_to_qq]
  end

  # ##Temp for debugging, please comment out when: not in use!
  role :Admin do
    has_permission_on :authorization_rules, to: :read
  end
end

privileges do
  privilege :arrange
  privilege :read, includes: %i[index show]
  privilege :invited
  privilege :participate
  privilege :current
end
