{"name": "auction", "packageManager": "yarn@1.22.22", "engines": {"node": "22.x"}, "private": true, "workspaces": [".storybook", "e2e"], "dependencies": {"@adeattwood/react-form": "^0.0.1-alpha.8", "@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.15", "@babel/preset-react": "^7.22.5", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-liquid": "^6.2.1", "@codemirror/lang-xml": "^6.1.0", "@hotwired/stimulus": "^3.2.2", "@hotwired/stimulus-webpack-helpers": "^1.0.1", "@hotwired/turbo-rails": "^8.0.4", "@rails/actioncable": "^8.0.0", "@rails/actiontext": "^8.0.0", "@rails/ujs": "^7.1.501", "@reduxjs/toolkit": "^2.1.0", "@stimulus-components/rails-nested-form": "^5.0.0", "@types/react-flatpickr": "^3.8.11", "@xmldom/xmldom": "^0.9.0", "axios": "^1.5.0", "babel-loader": "^10.0.0", "bootstrap": "^3.4.1", "bootstrap-icons": "^1.10.4", "bootstrap-toggle": "^2.2.2", "bootstrap5": "npm:bootstrap@5.3.3", "browser-cookies": "^1.2.0", "chosen-js": "1.8.7", "classnames": "^2.3.2", "clipboard": "2.0.11", "clsx": "^2.1.0", "codemirror": "^6.0.1", "codemirror-lang-liquid": "^1.0.0", "core-js": "^3.32.1", "date-fns": "^4.0.0", "dompurify": "^3.2.6", "dropzone": "^5.9.3", "enzyme-wait": "^1.0.9", "eslint-plugin-jest": "^29.0.0", "flatpickr": "^4.6.13", "highcharts": "^12.0.0", "highcharts-react-official": "^3.2.0", "html-react-parser": "^5.0.0", "i18next": "^25.0.0", "immer": "^10.0.3", "ion-rangeslider": "^2.3.1", "iso-3166-2": "^1.0.0", "jquery": "^3.7.1", "jquery-datetimepicker": "^2.5.21", "jquery-form": "^4.2.2", "jquery-match-height": "^0.7.2", "jquery-mousewheel": "^3.1.13", "jquery-placeholder": "2.3.1", "jquery-serializejson": "^3.2.1", "jquery-ui-dist": "^1.13.2", "jquery-ujs": "^1.2.3", "jquery-validation": "^1.19.5", "jquery.roundabout": "^2.4.4", "jscrollpane": "2.2.2", "jsdom": "^26.0.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "mark.js": "^8.11.1", "moment": "^2.29.4", "moment-timezone": "^0.6.0", "multiselect-react-dropdown": "2.0.25", "pivottable": "^2.23.0", "postcss": "^8.4.29", "prop-types": "^15.8.1", "qrcode-generator": "1.5.0", "react": "^17.0.2", "react-alert": "^7.0.3", "react-alert-template-oldschool-dark": "^1.0.1", "react-autocomplete": "^1.8.1", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.8.2", "react-bootstrap": "^2.8.0", "react-confirm": "^0.1.27", "react-datepicker": "^4.17.0", "react-dom": "^17.0.2", "react-draggable": "^4.4.5", "react-dropzone-component": "^3.2.0", "react-expanding-textarea": "^2.3.6", "react-flatpickr": "4.0.0", "react-load-script": "^0.0.6", "react-loader-advanced": "^1.7.1", "react-modal": "^3.16.1", "react-number-format": "^5.3.0", "react-paginate": "^8.2.0", "react-redux": "^8.0.0", "react-search-input": "^0.11.3", "react-select": "^5.7.4", "react-select2-wrapper": "^1.0.4-beta6", "react-spinners": "^0.17.0", "react-sticky-table": "^5.1.11", "react-tag-autocomplete": "^7.0.0", "react-timeout": "^2.0.1", "react-virtual": "^2.10.4", "react_ujs": "^2.7.1", "redux-form": "^8.3.9", "redux-persist": "^6.0.0", "redux-promise": "^0.6.0", "required-react-context": "^1.0.1", "shortcut-buttons-flatpickr": "^0.4.0", "simple-react-modal": "^0.5.1", "socket.io-client": "^2.3.1", "trix": "^1.3.3", "uglify-js": "^3.17.4", "url-search-params": "^1.1.0", "uuid": "^11.0.0", "valibot": "^1.0.0-beta.9", "yaml-loader": "^0.8.0"}, "devDependencies": {"@babel/eslint-parser": "^7.22.10", "@babel/plugin-transform-runtime": "^7.22.10", "@babel/preset-typescript": "7.27.1", "@babel/runtime": "^7.22.11", "@codemirror/lang-json": "^6.0.1", "@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@types/dompurify": "^3.2.0", "@types/enzyme": "^3.10.18", "@types/eslint-plugin-jsx-a11y": "^6.9.0", "@types/jest": "^30.0.0", "@types/jquery": "^3.5.29", "@types/lodash": "^4.17.13", "@types/node": "^22.0.0", "@types/rails__actioncable": "^6.1.10", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-modal": "^3.16.3", "@types/redux-form": "^8.3.11", "@types/uuid": "^10.0.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.8.0", "axios-mock-adapter": "^2.0.0", "babel-jest": "^30.0.0", "canvas": "^3.1.0", "compression-webpack-plugin": "^11.0.0", "css-loader": "^7.0.0", "css-minimizer-webpack-plugin": "^7.0.0", "enzyme": "^3.11.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-import-resolver-typescript": "4.4.4", "eslint-plugin-diff": "^2.0.3", "eslint-plugin-import-x": "^4.1.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.0.0", "globals": "^16.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "jest-fixed-jsdom": "^0.0.9", "jiti": "^2.0.0", "jscodeshift": "^17.0.0", "mini-css-extract-plugin": "^2.7.6", "mix-n-matchers": "^2.0.1", "msw": "^1", "patch-package": "^8.0.0", "postcss-flexbugs-fixes": "^5.0.2", "postcss-import": "^16.0.0", "postcss-loader": "^8.0.0", "postcss-preset-env": "^10.0.0", "prettier": "^3.0.3", "react-select-event": "^5.5.1", "redux-mock-store": "^1.5.4", "sass": "^1.66.1", "sass-loader": "^16.0.0", "shakapacker": "^8.0.0", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.9", "typed-scss-modules": "^8.0.0", "typescript": "5.8.3", "typescript-eslint": "8.35.1", "undici": "^7.0.0", "webpack": "^5.88.2", "webpack-assets-manifest": "^6.0.0", "webpack-cli": "^6.0.0", "webpack-dev-server": "^5.0.0", "webpack-merge": "^6.0.0", "webpack-sources": "^3.2.3"}, "resolutions": {"loader-utils": "^3.0.0", "redux": "^5.0.0", "jquery": "^3.7.1", "@testing-library/dom": "^10.0.0"}, "scripts": {"test": "export NODE_OPTIONS=--max_old_space_size=4096 && jest", "test-watch": "jest --watch --coverageDirectory tmp/cov", "lint": "yarn run lint:prettier && yarn run lint:js && yarn run lint:tsc", "lint:js": "eslint 'app/javascript' 'spec/react' '.storybook' 'e2e'", "lint:tsc": "tsc --noEmit --project ./tsconfig.json --project ./e2e/tsconfig.json", "lint:prettier": "prettier --check '{app,spec,vendor,.storybook}/**/*.{css,scss,sass}'", "storybook:build": "yarn workspace @marketdojo/storybook build", "tsm": "typed-scss-modules 'app/**/*.module.scss'", "postinstall": "patch-package"}, "jest": {"transform": {"\\.yml$": "<rootDir>/spec/react/__mocks__/yamlTransformer.js", "\\.(j|t)sx?$": "babel-jest"}, "testEnvironment": "jest-fixed-jsdom", "roots": ["spec/react", "spec/javascripts"], "moduleDirectories": ["node_modules", "app/javascript", "app/assets/javascripts"], "setupFilesAfterEnv": ["./spec/react/helpers.js", "./spec/react/setup.ts"], "moduleNameMapper": {"\\.(css|scss|less)$": "identity-obj-proxy", "@/(.*)$": "<rootDir>/app/javascript/$1", "@assets/(.*)$": "<rootDir>/app/assets/javascripts/$1", "@spec/(.*)$": "<rootDir>/spec/react/$1", "^.+\\.(svg)$": "<rootDir>/spec/react/__mocks__/fileMock.js"}, "collectCoverage": false, "coverageReporters": ["lcov", "json"], "collectCoverageFrom": ["**/*.{js,jsx,ts,tsx}", "!**/node_modules/**", "!**/spec/react/data/**/*.js", "!**/__testfixtures__/**"], "coverageDirectory": ".", "prettierPath": null}}