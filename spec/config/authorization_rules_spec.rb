# frozen_string_literal: true

require 'rails_helper'

# rubocop:disable RSpec/MultipleMemoizedHelpers
RSpec.describe 'AuthorizationRules', type: :authorization do
  # We use the declarative_authorization DSL directly rather than
  # interacting via controllers – that makes these specs fast and focused on
  # the rule behaviour itself.

  participant_types = %i[event_participant event_collaborator]

  let(:participant_user) { create(:participant_user) }

  # rubocop:disable RSpec/NestedGroups
  describe 'Event' do
    ################################################################################
    # :index on events
    ################################################################################
    describe 'AuthorizationRules::Events#index' do
      let!(:event) do
        event = Event.build(
          name: Faker::Commerce.product_name.pluralize,
          company: Company.build(name: Faker::Company.name),
          event_category_rfq: true,
          user: event_owner
        )

        ModelCallbackSkipper.skip_all do
          raise unless event.save(validate: false)
        end

        event
      end
      let(:event_owner) { create(:host_user) }
      let(:event_participant) do
        event_participant = build(:event_participant, accepted:, participating:, event:, user: participant_user)

        ModelCallbackSkipper.skip_all do
          raise unless event_participant.save(validate: false)

          own_collaborator = event_participant.build_own_collaborator(user: event_participant.user, event_participant:)

          raise unless own_collaborator.save(validate: false)
        end

        event_participant
      end
      let(:participating) { true }
      let(:accepted) { true }

      shared_examples 'a rule that lists the event' do
        it_behaves_like 'a rule that allows access', :index do
          let(:object) { event }
        end

        context 'when the host does not consent to their participation' do
          let(:accepted) { false }

          it_behaves_like 'a rule that allows access', :index do
            let(:object) { event }
          end
        end

        context 'when the supplier does not consent to their participation' do
          let(:participating) { false }

          it_behaves_like 'a rule that allows access', :index do
            let(:object) { event }
          end
        end
      end

      context 'when outside the sandpit' do
        before { event.update_column(:sandpit_event, false) }

        context 'as an event_collaborator' do
          let(:current_user) { create(:event_collaborator, event_participant:).user }

          context 'with a non-sandpit event' do
            it_behaves_like 'a rule that lists the event'
          end

          context 'with a sandpit event' do # impossible via the UI
            before { event.update_column(:sandpit_event, true) }

            it_behaves_like 'a rule that denies access', :index do
              let(:object) { event }
            end
          end
        end

        context 'as lead participant who is somehow missing their own collaborator record' do
          let(:current_user) { event_participant.user }

          before do
            event_participant.own_collaborator.delete
          end

          context 'with a non-sandpit event' do
            it_behaves_like 'a rule that lists the event'
          end

          context 'with a sandpit event' do # impossible via the UI
            before { event.update_column(:sandpit_event, true) }

            it_behaves_like 'a rule that denies access', :index do
              let(:object) { event }
            end
          end
        end

        context 'as a random participant' do
          let(:current_user) { create(:participant_user) }

          it_behaves_like 'a rule that denies access', :index do
            let(:object) { event }
          end
        end
      end

      context 'when inside the sandpit as a host' do
        before do
          current_user.instance_variable_set(:@in_sandpit, true)

          allow(current_user).to receive_messages(
            acting_as: 'Participant',
            acting_as_id: acting_as_user.id,
            acting_as_user:
          )
        end

        context 'who is the owner of a sandpit event' do
          let(:current_user) { event_owner }
          let(:participant_user) { create(:sandpit_user, user: create(:participant_user)).user }

          before { event.update_column(:sandpit_event, true) }

          context 'while acting as a lead participant' do
            let(:acting_as_user) { event_participant.user }

            it_behaves_like 'a rule that lists the event'

            context 'who is not part of the event' do
              let(:event_participant) { create(:event_participant) }

              it_behaves_like 'a rule that denies access', :index do
                let(:object) { event }
              end
            end
          end

          context 'while acting as a collaborator' do # impossible via the UI
            let(:acting_as_user) { create(:event_collaborator, event_participant:).user }

            it_behaves_like 'a rule that lists the event'

            context 'who is not part of the event' do
              let(:event_participant) { create(:event_participant) }

              it_behaves_like 'a rule that denies access', :index do
                let(:object) { event }
              end
            end
          end
        end

        context 'when trying to access a non-sandpit event that they also own' do
          let(:current_user) { event_owner }

          before { event.update_column(:sandpit_event, false) }

          context 'while acting as a lead participant' do
            let(:acting_as_user) { event_participant.user }

            it_behaves_like 'a rule that denies access', :index do
              let(:object) { event }
            end
          end

          context 'while acting as a collaborator' do # impossible via the UI
            let(:acting_as_user) { create(:event_collaborator, event_participant:).user }

            it_behaves_like 'a rule that denies access', :index do
              let(:object) { event }
            end
          end
        end

        context 'when trying to access a sandpit event that they do not own' do
          let(:current_user) { create(:host_user) }

          context 'while acting as a participant' do # impossible via the UI
            let(:acting_as_user) { event_participant.user }

            it_behaves_like 'a rule that denies access', :index do
              let(:object) { event }
            end
          end
        end
      end

      context 'as a sandpit participant directly' do # impossible via the UI
        let(:current_user) { participant_user }
        let(:acting_as_user) { participant_user }

        before do
          current_user.instance_variable_set(:@in_sandpit, true)
        end

        it_behaves_like 'a rule that denies access', :index do
          let(:object) { event }
        end
      end
    end

    ################################################################################
    # :read / :event_exchange_rate on events
    ################################################################################
    %i[read event_exchange_rate].each do |privilege|
      describe "AuthorizationRules::Events##{privilege}" do
        let(:event) do
          event = Event.build(
            name: Faker::Commerce.product_name.pluralize,
            event_category_rfq:,
            event_category_auction:,
            quick_quotes:,
            user: build(:host_user, company: Company.build(name: Faker::Company.name)),
            lots:
          )

          ModelCallbackSkipper.skip_all do
            raise unless event.save(validate: false)
          end

          event
        end
        let(:lots) { Array.new(2) { Lot.build(complex_structure: false, name: Faker::Commerce.product_name) } }
        let(:event_participant) do
          event_participant = build(:event_participant, accepted:, participating:, event:)

          ModelCallbackSkipper.skip_all do
            raise unless event_participant.save(validate: false)
          end

          event_participant
        end
        let(:current_user) { event_participant.user }
        let(:participating) { true }
        let(:accepted) { true }
        let(:event_category_rfq) { true }
        let(:event_category_auction) { false }
        let(:quick_quotes) { false }

        shared_examples 'a rule that can allow any active suppliers' do |always_deny:|
          context 'when both host and supplier want the supplier to participate' do
            if always_deny
              it_behaves_like 'a rule that denies access', privilege do
                let(:object) { event }
              end
            else
              it_behaves_like 'a rule that allows access', privilege do
                let(:object) { event }
              end
            end
          end

          context 'and the supplier is not interested in participating' do
            let(:participating) { false }

            it_behaves_like 'a rule that denies access', privilege do
              let(:object) { event }
            end
          end

          context 'and the host does not want the supplier to participate' do
            let(:accepted) { false }

            it_behaves_like 'a rule that denies access', privilege do
              let(:object) { event }
            end
          end
        end

        shared_examples 'a rule that can allow suppliers not eliminated in a prior round' do |always_deny:|
          context 'with rounds' do
            let!(:event_rounds) do
              rounds = build_list(:event_round, 2, event:)

              ModelCallbackSkipper.skip_all do
                raise unless rounds.all? { |r| r.save(validate: false) }
              end

              rounds
            end

            context 'and the supplier is through to the latest round' do
              before do
                last_round = event_rounds.max_by(&:id)
                last_round.event_participants << event_participant

                ModelCallbackSkipper.skip_all do
                  raise unless last_round.save(validate: false)
                end
              end

              if always_deny
                it_behaves_like 'a rule that denies access', privilege do
                  let(:object) { event }
                end
              else
                it_behaves_like 'a rule that can allow any active suppliers', always_deny:
              end
            end

            context 'and the supplier was eliminated in a prior round' do
              it_behaves_like 'a rule that denies access', privilege do
                let(:object) { event }
              end
            end
          end

          context 'without rounds' do
            it_behaves_like 'a rule that can allow any active suppliers', always_deny:
          end
        end

        context 'as a supplier' do
          participant_types.each do |participant_type|
            context "who is an #{participant_type} on the event" do
              if participant_type == :event_collaborator
                let(:current_user) do
                  create(:event_collaborator, event_participant:).user
                end
              end

              readable_states = %w[completed edit pause pending post_rfq_pre_auction rfq running]
              unreadable_states = Event.aasm.states.map { |state| state.name.to_s } - readable_states
              states_sample = readable_states.sample(2) << unreadable_states.sample

              states_sample.each do |state|
                context "when the event state is '#{state}'" do
                  always_deny = unreadable_states.include?(state)

                  before do
                    event.update_column(:current_state, state)
                  end

                  context 'with simple lots' do
                    context 'and an RFQ' do
                      let(:event_category_rfq) { true }
                      let(:event_category_auction) { false }

                      context 'without an auction' do
                        it_behaves_like 'a rule that can allow suppliers not eliminated in a prior round', always_deny:
                      end

                      context 'with an auction' do
                        let(:event_category_auction) { true }

                        it_behaves_like 'a rule that can allow suppliers not eliminated in a prior round', always_deny:
                      end
                    end

                    context 'and an auction' do
                      let(:event_category_auction) { true }
                      let(:event_category_rfq) { false }

                      context 'without an RFQ' do # Think this is impossible via the UI?
                        it_behaves_like 'a rule that can allow any active suppliers', always_deny:
                      end
                    end
                  end

                  context 'with advanced lots' do
                    before do
                      event.lots.update_all(complex_structure: true)
                    end

                    context 'and an RFQ' do
                      let(:event_category_rfq) { true }
                      let(:event_category_auction) { false }

                      context 'without an auction' do
                        it_behaves_like 'a rule that can allow suppliers not eliminated in a prior round', always_deny:
                      end

                      context 'with an auction' do
                        let(:event_category_auction) { true }

                        it_behaves_like 'a rule that can allow suppliers not eliminated in a prior round', always_deny:
                      end
                    end

                    context 'and an auction' do
                      let(:event_category_auction) { true }
                      let(:event_category_rfq) { false }

                      context 'without an RFQ' do # Think this is impossible via the UI?
                        it_behaves_like 'a rule that can allow suppliers not eliminated in a prior round', always_deny:
                      end
                    end
                  end

                  context 'with quick_quotes' do
                    let(:event_category_auction) { false }
                    let(:quick_quotes) { true }

                    it_behaves_like 'a rule that can allow suppliers not eliminated in a prior round', always_deny:
                  end
                end
              end
            end

            context 'who would otherwise see an event' do
              let(:object) { event }

              before do
                event.update_column(:current_state, :running)
              end

              # Sanity check (running event without rounds should be visible)
              it_behaves_like 'a rule that allows access', privilege

              context 'but the event is in the sandpit' do
                before do
                  event.update_column(:sandpit_event, true)
                end

                it_behaves_like 'a rule that denies access', privilege
              end
            end
          end

          context 'who is an uninvolved colleague of a user participating in the event' do
            let(:current_user) { create(:participant_user, company: event_participant.user.company) }

            it_behaves_like 'a rule that denies access', privilege do
              let(:object) { event }
            end
          end

          context 'who is participating in some other event' do
            let(:current_user) { create(:event_participant).user }

            it_behaves_like 'a rule that denies access', privilege do
              let(:object) { event }
            end
          end
        end

        context 'as a Host' do
          let(:current_user) { create(:host_user, :in_sandpit) }

          before do
            event.update_column(:current_state, :running) # running events without rounds are visible
          end

          context 'while inside the sandpit' do
            before do
              current_user.instance_variable_set(:@in_sandpit, true)
            end

            context 'when trying to access their own sandpit event' do
              before do
                event.update_columns(sandpit_event: true, user_id: current_user.id)
              end

              context 'while acting as a lead participant' do
                let(:participant_user) { create(:sandpit_user, user: create(:participant_user)).user }

                before do
                  allow(current_user).to receive_messages(
                    acting_as: 'Participant',
                    acting_as_id: participant_user.id,
                    acting_as_user: participant_user
                  )
                end

                context 'who is part of the event' do
                  before { event_participant.update_column(:user_id, participant_user.id) }

                  it_behaves_like 'a rule that allows access', privilege do
                    let(:object) { event }
                  end
                end

                context 'who is not part of the event' do
                  it_behaves_like 'a rule that denies access', privilege do
                    let(:object) { event }
                  end
                end
              end

              context 'while acting as a collaborator' do # impossible via the UI
                before do
                  allow(current_user).to receive_messages(
                    acting_as: 'Participant',
                    acting_as_id: event_collaborator.user.id,
                    acting_as_user: event_collaborator.user
                  )
                end

                context 'who is part of the event' do
                  let(:event_collaborator) { create(:event_collaborator, event_participant:) }

                  it_behaves_like 'a rule that allows access', privilege do
                    let(:object) { event }
                  end
                end

                context 'who is not part of the event' do
                  let(:event_collaborator) { create(:event_collaborator) }

                  it_behaves_like 'a rule that denies access', privilege do
                    let(:object) { event }
                  end
                end
              end
            end

            context 'when trying to access their own non-sandpit event' do
              before do
                event.update_columns(sandpit_event: false, user_id: current_user.id)
              end

              context 'while acting as a lead participant' do
                before do
                  allow(current_user).to receive_messages(
                    acting_as: 'Participant',
                    acting_as_id: participant_user.id,
                    acting_as_user: participant_user
                  )
                end

                context 'who is part of the event' do
                  before { event_participant.update_column(:user_id, participant_user.id) }

                  it_behaves_like 'a rule that denies access', privilege do
                    let(:object) { event }
                  end
                end
              end

              context 'while acting as a collaborator' do # impossible via the UI
                before do
                  allow(current_user).to receive_messages(
                    acting_as: 'Participant',
                    acting_as_id: event_collaborator.user.id,
                    acting_as_user: event_collaborator.user
                  )
                end

                context 'who is part of the event' do
                  let(:event_collaborator) { create(:event_collaborator, event_participant:) }

                  it_behaves_like 'a rule that denies access', privilege do
                    let(:object) { event }
                  end
                end
              end
            end

            context 'when trying to access a sandpit event that they do not own' do
              before do
                event.update_columns(sandpit_event: true, user_id: create(:host_user).id)
              end

              context 'while acting as a lead participant' do
                let(:participant_user) { create(:sandpit_user, user: create(:participant_user)).user }

                before do
                  allow(current_user).to receive_messages(
                    acting_as: 'Participant',
                    acting_as_id: participant_user.id,
                    acting_as_user: participant_user
                  )
                end

                context 'who is part of the event' do # impossible via the UI
                  before { event_participant.update_column(:user_id, participant_user.id) }

                  it_behaves_like 'a rule that denies access', privilege do
                    let(:object) { event }
                  end
                end
              end

              context 'while acting as a collaborator' do # impossible via the UI
                before do
                  allow(current_user).to receive_messages(
                    acting_as: 'Participant',
                    acting_as_id: event_collaborator.user.id,
                    acting_as_user: event_collaborator.user
                  )
                end

                context 'who is part of the event' do # impossible via the UI
                  let(:event_collaborator) { create(:event_collaborator, event_participant:) }

                  it_behaves_like 'a rule that denies access', privilege do
                    let(:object) { event }
                  end
                end
              end
            end
          end
        end
      end
      # rubocop:enable RSpec/NestedGroups
    end
  end

  describe 'QuestionnaireAnswer' do
    let(:object) { questionnaire_answer }
    let(:questionnaire_answer) { create(:questionnaire_answer, event:) }
    let(:event) do
      event = Event.build(
        company: Company.build(name: Faker::Company.name),
        user: build(:host_user)
      )

      ModelCallbackSkipper.skip_all do
        raise unless event.save(validate: false)
      end

      event
    end
    let(:event_participant) { create(:event_participant, event:) }

    before do
      allow(Authorization::Engine.instance).to receive(:permit?).and_call_original
    end

    %i[export_xlsx show upload_questionnaire_response].each do |privilege|
      describe "AuthorizationRules::QuestionnaireAnswers##{privilege}" do
        let(:event_participant) { create(:event_participant, event:, accepted:, participating:) }
        let(:questionnaire_answer) do
          create(
            :questionnaire_answer,
            event:,
            market_dojo_event_participant: event_participant,
            participant_id: event_participant.user.id
          )
        end
        let(:participant) { event_participant }
        let(:current_user) { event_participant.user }
        let(:accepted) { true }
        let(:participating) { true }
        let(:other_object_authorized) { true }

        before do
          allow(Authorization::Engine.instance).to receive(:permit?).with(
            :read,
            object: instance_of(Event),
            user: instance_of(User)
          ).and_return(other_object_authorized)
        end

        context 'as a supplier' do
          participant_types.each do |participant_type|
            context "who is an #{participant_type} on the event" do
              if participant_type == :event_collaborator
                let(:current_user) { create(:event_collaborator, event_participant:).user }
              end

              it_behaves_like 'a rule that allows access', privilege

              context 'when the event_participant did not create the questionnaire_answer' do
                before do
                  some_other_participant = create(:participant_user)

                  questionnaire_answer.update_column(:participant_id, some_other_participant.id)
                end

                it_behaves_like 'a rule that allows access', privilege
              end

              context 'when the host does not consent to their participation' do
                let(:accepted) { false }

                it_behaves_like 'a rule that denies access', privilege
              end

              context 'when the supplier does not consent to their participation' do
                let(:participating) { false }

                it_behaves_like 'a rule that denies access', privilege
              end
            end
          end
        end
      end
    end

    %i[edit update].each do |privilege|
      describe "AuthorizationRules::QuestionnaireAnswers##{privilege}" do
        let(:event_participant) do
          event_participant = build(:event_participant, event:, accepted:, participating:, user: participant_user)

          ModelCallbackSkipper.skip_all { raise unless event_participant.save(validate: false) }

          event_participant
        end
        let(:questionnaire_answer) do
          create(
            :questionnaire_answer,
            event:,
            market_dojo_event_participant: event_participant,
            participant_id: event_participant.user.id
          )
        end
        let(:participant) { event_participant }
        let(:current_user) { event_participant.user }
        let(:accepted) { true }
        let(:participating) { true }

        context 'as a supplier' do
          participant_types.each do |participant_type|
            context "who is an #{participant_type} on the event" do
              if participant_type == :event_collaborator
                let(:current_user) { create(:event_collaborator, event_participant:).user }
              end

              it_behaves_like 'a rule that allows access', privilege

              context 'when the event_participant did not create the questionnaire_answer' do
                before do
                  some_other_participant = create(:participant_user)
                  questionnaire_answer.update_column(:participant_id, some_other_participant.id)
                end

                # Both event participants and collaborators should be able to access questionnaire answers
                # from their event participant, regardless of who originally created them
                it_behaves_like 'a rule that allows access', privilege
              end

              context 'when the host does not consent to their participation' do
                let(:accepted) { false }

                it_behaves_like 'a rule that denies access', privilege
              end

              context 'when the supplier does not consent to their participation' do
                let(:participating) { false }

                it_behaves_like 'a rule that denies access', privilege
              end
            end
          end
        end
      end
    end

    describe 'AuthorizationRules::QuestionnaireAnswers#index' do
      privilege = :index

      let(:current_user) { event_participant.user }

      context 'as a supplier' do
        participant_types.each do |participant_type|
          context "who is an #{participant_type} on the event" do
            if participant_type == :event_collaborator
              let(:current_user) do
                create(:event_collaborator, event_participant:).user
              end
            end

            it_behaves_like 'a rule that delegates to other rules', { event: :read }, privilege:
          end
        end
      end
    end
  end

  describe 'Questionnaire' do
    let(:current_user) { event_participant.user }
    let(:object) { questionnaire }
    let(:other_object) { event }
    let(:questionnaire) { create(:questionnaire, event:) }
    let(:event) do
      event = Event.build(
        company: Company.build(name: Faker::Company.name),
        user: build(:host_user)
      )

      ModelCallbackSkipper.skip_all { raise unless event.save(validate: false) }

      event
    end
    let(:event_participant) do
      event_participant = build(:event_participant, event:, user: participant_user)

      ModelCallbackSkipper.skip_all { raise unless event_participant.save(validate: false) }

      event_participant
    end

    %i[index show].each do |privilege|
      describe "AuthorizationRules::Questionnaires##{privilege}" do
        context 'as a supplier' do
          participant_types.each do |participant_type|
            context "who is an #{participant_type} on the event" do
              if participant_type == :event_collaborator
                let(:current_user) { create(:event_collaborator, event_participant:).user }
              end

              it_behaves_like 'a rule that delegates to other rules', { event: :read }, privilege:
            end
          end
        end
      end
    end
  end

  describe 'QuestionnaireQuestionsAnswer' do
    describe 'AuthorizationRules::QuestionnaireQuestionsAnswer#update' do
      privilege = :update

      let(:current_user) { event_participant.user }
      let(:object) { questionnaire_questions_answer }
      let(:event) do
        event = Event.build(
          company: Company.build(name: Faker::Company.name),
          user: build(:host_user)
        )

        ModelCallbackSkipper.skip_all { raise unless event.save(validate: false) }

        event
      end
      let(:event_participant) do
        event_participant = build(:event_participant, event:, user: participant_user)

        ModelCallbackSkipper.skip_all { raise unless event_participant.save(validate: false) }

        event_participant
      end
      let(:questionnaire_answer) do
        create(
          :questionnaire_answer,
          event:,
          participant: event_participant.user,
          event_participant_id: event_participant.id
        )
      end
      let(:questionnaire_questions_answer) { create(:questionnaire_questions_answer, questionnaire_answer:) }

      context 'as a supplier' do
        participant_types.each do |participant_type|
          context "who is an #{participant_type} on the event" do
            if participant_type == :event_collaborator
              let(:current_user) { create(:event_collaborator, event_participant:).user }

            end

            it_behaves_like 'a rule that delegates to other rules',
                            { event: :edit, questionnaire_answer: :edit },
                            privilege:
          end
        end
      end
    end
  end

  describe 'Lot' do
    let(:current_user) { event_participant.user }
    let(:object) { lot }
    let(:other_object) { event }
    let(:lot) { create(:lot, event:) }
    let(:event) do
      event = Event.build(
        company: Company.build(name: Faker::Company.name),
        user: build(:host_user)
      )

      ModelCallbackSkipper.skip_all { raise unless event.save(validate: false) }

      event
    end
    let(:event_participant) do
      event_participant = build(:event_participant, event:, user: participant_user)

      ModelCallbackSkipper.skip_all { raise unless event_participant.save(validate: false) }

      event_participant
    end

    %i[read monitor monitor_status].each do |privilege|
      describe "AuthorizationRules::Lots##{privilege}" do
        context 'as a supplier' do
          participant_types.each do |participant_type|
            context "who is an #{participant_type} on the event" do
              if participant_type == :event_collaborator
                let(:current_user) { create(:event_collaborator, event_participant:).user }
              end

              it_behaves_like 'a rule that delegates to other rules', { event: :read }, privilege:
            end
          end
        end
      end
    end
  end

  describe 'EventRound' do
    let(:current_user) { event_participant.user }
    let(:object) { event_round }
    let(:other_object) { event }
    let(:event_round) { create(:event_round, event:) }
    let(:event) do
      event = Event.build(
        company: Company.build(name: Faker::Company.name),
        user: build(:host_user)
      )

      ModelCallbackSkipper.skip_all { raise unless event.save(validate: false) }

      event
    end
    let(:event_participant) do
      event_participant = build(:event_participant, event:, user: participant_user)

      ModelCallbackSkipper.skip_all { raise unless event_participant.save(validate: false) }

      event_participant
    end

    describe 'AuthorizationRules::EventRounds#show' do
      context 'as a supplier' do
        participant_types.each do |participant_type|
          if participant_type == :event_collaborator
            let(:current_user) { create(:event_collaborator, event_participant:).user }
          end

          it_behaves_like 'a rule that delegates to other rules', { event: :read }, privilege: :show
        end
      end
    end
  end

  describe 'UnitOfMeasure' do
    let(:current_user) { event_participant.user }
    let(:object) { unit_of_measure }
    let(:unit_of_measure) { create(:unit_of_measure) }
    let(:event_participant) do
      event_participant = build(:event_participant, user: participant_user)

      ModelCallbackSkipper.skip_all { raise unless event_participant.save(validate: false) }

      event_participant
    end

    describe 'AuthorizationRules::UnitOfMeasures#convert' do
      context 'as a supplier' do
        participant_types.each do |participant_type|
          context "who is an #{participant_type} on the event" do
            if participant_type == :event_collaborator
              let(:current_user) { create(:event_collaborator, event_participant:).user }
            end

            it_behaves_like 'a rule that allows access', :convert do
              let(:object) { unit_of_measure }
            end
          end
        end
      end
    end
  end
end
# rubocop:enable RSpec/MultipleMemoizedHelpers
