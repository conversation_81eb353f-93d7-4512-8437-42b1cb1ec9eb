# frozen_string_literal: true

require 'rails_helper'

describe ContractsController do
  include ActiveJob::TestHelper
  include ContractHelpers
  include ContractsControllerHelpers
  # Superhosts have permission, due to logic in user.allow_contract_action
  # we want to test with someone who is not superhost, so we create a superhost but do not use them for testing
  let!(:superhost) { create(:host_user, :with_licence) }
  let!(:user) { create(:host_user, :with_licence, company: superhost.company) }
  let!(:other_user) { create(:host_user, :with_licence, company: superhost.company) }
  let!(:user2) { create(:host_user, :with_licence, company: superhost.company, deactivated: true) }
  let!(:no_sim_user) { create(:host_user, :with_licence) }
  let!(:sim_dojo_event) { create(:sim_dojo_event_with_participant, user:) }

  before do
    sign_in(user)
  end

  describe '#download_file_document' do
    let!(:contract) { create_contract(user, 'Contract', '123') }
    let!(:versioned_file) { create(:document_type_versioned_file, user:, company: user.company) }
    let!(:file_content) do
      create(:versioned_file_document, versioned_file:, user: versioned_file.user)
    end

    context 'when document get downloaded' do
      before do
        create(:contract_file, contract:, versioned_file:)
        get :download_file_document,
            params: {
              id: contract.to_param,
              file_id: versioned_file.id,
              document_id: file_content.id
            }
      end

      it 'gets success if document file downloaded' do
        expect(response).to have_http_status :ok
      end

      it 'downloads correct document from file' do
        expect(response.headers['Content-Disposition']).to include(file_content.display_name)
      end
    end

    context 'when document does not belong to contract owner' do
      before do
        cf = create(:contract_file, contract:, versioned_file:)

        cf.versioned_file.documents.first.user = create(:host_user, company: user.company)
        cf.versioned_file.documents.first.save
        get :download_file_document,
            params: {
              id: contract.to_param,
              file_id: versioned_file.id,
              document_id: file_content.id
            }
      end

      it 'gets success if document file downloaded' do
        expect(response).to have_http_status :ok
      end

      it 'downloads correct document from file' do
        expect(response.headers['Content-Disposition']).to include(file_content.display_name)
      end
    end

    context 'when document get deleted from storage' do
      before do
        create(:contract_file, contract:, versioned_file:)

        allow_any_instance_of(DocumentUploader).to receive(:url).and_return('/test')
        allow_any_instance_of(DocumentUploader).to receive(:path).and_return('/test')
      end

      it 'shows error message if document not present in storage' do
        get :download_file_document,
            params: {
              id: contract.to_param,
              file_id: versioned_file.id,
              document_id: file_content.id
            }
        expect(flash['alert']).to include(t(:document_missing, document: file_content.display_name))
      end
    end
  end
end
