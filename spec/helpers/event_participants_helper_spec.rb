# frozen_string_literal: true

require 'rails_helper'

describe EventParticipantsHelper do
  fixtures :events, :users, :event_participants
  describe 'host acceptance status html' do
    let(:event) { stub_model(Event) }
    let(:event_participant) { mock_model(EventParticipant, event:) }

    describe 'when event complete' do
      before do
        allow(event).to receive(:current_state).and_return('completed')
      end

      after do
        expect(host_acceptance_status_html(event, event_participant, true))
          .not_to match(/matte dropdown/)
      end

      it 'is correct if participant accepted into event' do
        # Green tick with hover text -  event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return('event_participants.show.accepted_html')
        expect(host_acceptance_status_html(event, event_participant, true))
          .to match(/button-yes/)
      end

      it 'is correct if participant declined from event' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return(:Declined)
        expect(host_acceptance_status_html(event, event_participant, true))
          .to match(/button-no/)
      end

      it 'is correct if participant not accepted into event' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return(:invitation_expired)
        expect(host_acceptance_status_html(event, event_participant, true))
          .to match(/button-orange/)
      end
    end

    describe 'when event pending' do
      before do
        allow(event).to receive(:current_state).and_return('pending')
        allow(event).to receive(:passed_final_deadline?).and_return(false)
      end

      after do
        expect(host_acceptance_status_html(event, event_participant, true))
          .to match(/btn-group-sm dropdown/)
      end

      it 'is correct if participant accepted into event' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return('event_participants.show.accepted_html')
        expect(host_acceptance_status_html(event, event_participant, true))
          .to include('button-yes')
      end

      it 'is correct if participant declined from event' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return(:Declined)
        expect(host_acceptance_status_html(event, event_participant, true))
          .to match(/button-no/)
      end

      it 'is correct if participant not accepted into event' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return(:invitation_expired)
        expect(host_acceptance_status_html(event, event_participant, true))
          .to match(/button-orange/)
      end
    end

    describe 'read only user' do
      before do
        allow(event).to receive(:current_state).and_return('pending')
        allow(event).to receive(:passed_final_deadline?).and_return(false)
      end

      after do
        expect(host_acceptance_status_html(event, event_participant, false))
          .not_to match(/matte dropdown/)
      end

      it 'is correct if participant accepted into event' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return('event_participants.show.accepted_html')
        expect(host_acceptance_status_html(event, event_participant, false))
          .to match(/button-yes/)
      end

      it 'is correct if participant declined from event' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return(:Declined)
        expect(host_acceptance_status_html(event, event_participant, false))
          .to match(/button-no/)
      end

      it 'is correct if participant not accepted into event' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:host_acceptance_status_symbol)
          .and_return(:invitation_expired)
        expect(host_acceptance_status_html(event, event_participant, false))
          .to match(/button-orange/)
      end
    end
  end

  describe 'participant approval status html' do
    let(:event) { stub_model(Event) }
    let(:event_participant) { mock_model(EventParticipant, event:) }

    describe 'when event complete' do
      before do
        allow(event).to receive(:current_state).and_return('completed')
      end

      it 'correct if participant accepted invite and host accepted' do
        # Green tick with hover text - event completed -
        # great and this currently exists
        allow(event_participant).to receive(:acceptance_status_symbol)
          .and_return(:event_completed)
        expect(participant_approval_status_html(event_participant))
          .to match(/fa-check/)
      end

      it 'is correct if participant accepts invite and is declined by host' do
        # Red cross with hover text 'Declined by host'
        allow(event_participant).to receive(:acceptance_status_symbol)
          .and_return(:event_participant_rejected_by_host)
        expect(participant_approval_status_html(event_participant))
          .to match(/fa-times/)
      end

      it 'correct if participant accepts invite and is not approved by host' do
        # Red cross with hover text 'Not approved'
        allow(event_participant).to receive(:acceptance_status_symbol)
          .and_return(:event_participant_not_approved)
        expect(participant_approval_status_html(event_participant))
          .to match(/fa-times/)
      end

      it 'is correct if participant declines invite' do
        # Red cross with hover text 'Declined Invite'
        allow(event_participant).to receive(:acceptance_status_symbol)
          .and_return(:event_participant_declined)
        expect(participant_approval_status_html(event_participant))
          .to match(/fa-times/)
      end

      it 'is correct if participant does not do anything with invite' do
        # Amber box icon with hover text 'Invitation expired'
        allow(event_participant).to receive(:acceptance_status_symbol)
          .and_return(:invitation_expired)
        expect(participant_approval_status_html(event_participant))
          .to match(/fa-ellipsis-h/)
      end
    end

    describe 'when event pending' do
      before do
        allow(event).to receive(:current_state).and_return('pending')
      end

      it 'is correct if participant has not accepted invite' do
        # Amber with hover text - xxc
        allow(event_participant).to receive(:acceptance_status_symbol)
          .and_return(:event_participant_pending_your_acceptance)
        expect(participant_approval_status_html(event_participant))
          .to match(/fa-ellipsis-h/)
      end

      it 'is correct if host has not accepted' do
        allow(event_participant).to receive(:acceptance_status_symbol)
          .and_return(:event_participant_pending_host_acceptance)
        expect(participant_approval_status_html(event_participant))
          .to match(/fa-ellipsis-h/)
      end

      it 'is correct if host has rejected' do
        allow(event_participant).to receive(:acceptance_status_symbol)
          .and_return(:event_participant_rejected_by_host)
        expect(participant_approval_status_html(event_participant))
          .to match(/fa-times/)
      end
    end
  end

  describe '#onboarding_status_html' do
    let(:ep) { stub_model(EventParticipant) }

    it 'renders nil if status nil' do
      allow(ep).to receive(:onboarding_status).and_return([nil, nil])
      expect(onboarding_status_html(ep)).to be_nil
    end

    it 'renders nil for onboarded participant' do
      allow(ep).to receive(:onboarding_status).and_return([:onboard, nil])
      expect(onboarding_status_html(ep)).to be_nil
    end

    it 'renders html for expiring participant' do
      allow(ep).to receive(:onboarding_status)
        .and_return([:expiring, 1.year.ago])
      expect(onboarding_status_html(ep)).to match(/Expiring/)
    end

    it 'renders html for expired participant' do
      allow(ep).to receive(:onboarding_status)
        .and_return([:expired, 1.year.ago])
      expect(onboarding_status_html(ep)).to match(/Expired/)
    end

    it 'renders html for rejected participant' do
      allow(ep).to receive(:onboarding_status).and_return([:rejected, nil])
      expect(onboarding_status_html(ep)).to match(/Rejected \(onboarding\)/)
    end
  end

  describe '#registered_status_html' do
    let(:ep) { stub_model(EventParticipant) }

    it 'renders if registered' do
      allow(ep).to receive_message_chain(:user, :registered)
        .and_return(['yes'])
      expect(registered_status_html(ep)).to match(/Registered/)
    end

    it 'renders if participating' do
      allow(ep).to receive_message_chain(:user, :registered)
        .and_return(['no'])
      expect(registered_status_html(ep)).to match(/Not registered/)
    end
  end

  describe '#participating_status_html' do
    context 'when the user of the event participant is registered' do
      let(:ep) { stub_model(EventParticipant, user: stub_model(User, registered: ['yes'])) }

      context 'when the user is participating' do
        it 'returns html with a green tick and the text "Accepted invite"' do
          allow(ep).to receive(:participating).and_return(true)
          expect(participating_status_html(ep)).to eq(
            '<div class="green-color"><span class="fa fa-check"></span><text> Accepted invite</text></div>'
          )
        end
      end

      context 'when the user is not participating' do
        it 'returns html with a red cross and the text "Declined event"' do
          allow(ep).to receive(:participating).and_return(false)
          expect(participating_status_html(ep)).to eq(
            '<div class="red-color"><span class="fa fa-times"></span><text> Declined event</text></div>'
          )
        end
      end

      context 'when the user has not responded to the invitation' do
        before do
          allow(ep).to receive(:participating).and_return(nil)
        end

        context 'with an invitation that has repeatedly failed to send' do
          it 'returns nil"' do
            allow(ep).to receive(:permanent_failure?).and_return(true)
            expect(participating_status_html(ep)).to be_nil
          end
        end

        context 'with an invitation that has been sent successfully' do
          it 'returns html with a red cross and the text "Not accepted invite"' do
            allow(ep).to receive(:permanent_failure?).and_return(false)
            expect(participating_status_html(ep)).to eq(
              '<div class="red-color"><span class="fa fa-times"></span><text> Not accepted invite</text></div>'
            )
          end
        end
      end
    end

    context 'when the user of the event participant is not registered' do
      let(:ep) { stub_model(EventParticipant, user: stub_model(User, registered: ['no'])) }

      context 'when the user is participating' do
        it 'returns html with a green tick and the text "Accepted invite"' do
          allow(ep).to receive(:participating).and_return(true)
          expect(participating_status_html(ep)).to eq(
            '<div class="green-color"><span class="fa fa-check"></span><text> Accepted invite</text></div>'
          )
        end
      end

      context 'when the user is not participating' do
        it 'returns html with a red cross and the text "Declined"' do
          allow(ep).to receive(:participating).and_return(false)
          expect(participating_status_html(ep)).to eq(
            '<div class="red-color"><span class="fa fa-times"></span><text> Declined event</text></div>'
          )
        end
      end

      context 'when the user has not responded to the invitation' do
        it 'returns nil' do
          allow(ep).to receive(:participating).and_return(nil)
          expect(participating_status_html(ep)).to be_nil
        end
      end
    end
  end

  describe '#status_html' do
    let(:outer_class) { 'outer-class' }
    let(:inner_class) { 'inner-class' }
    let(:text) { 'some text' }
    let(:tooltip) { 'some tooltip' }

    it 'returns a html string with an outer class, inner class, and text' do
      want = status_html(outer_class, inner_class, text)
      got = %r{<div class="#{outer_class}".*<span class="#{inner_class}"></span>\s*<text>\s*#{text}\s*</text></div>}

      expect(want).to match(got)
    end

    it 'returns a html string with outer class, inner class, text, and tooltip' do
      want = status_html(outer_class, inner_class, text, tooltip)
      got = %r{<div class="#{outer_class}".*title="#{tooltip}".*<span class="#{inner_class}"></span>\s*<text>\s*#{text}\s*</text></div>}

      expect(want).to match(got)
    end
  end

  describe 'Email Status Methods' do
    let(:user) { stub_model(User) }
    let(:event) { stub_model(Event) }
    let(:ep) { stub_model(EventParticipant, event:, user:) }

    let(:email_tracking_opened) do
      stub_model(EmailTracking, recipient_id: ep.user_id, status: 'opened')
    end
    let(:email_tracking_delivered) do
      stub_model(EmailTracking, recipient_id: ep.user_id, status: 'delivered')
    end

    describe '#invalid_email_status_html' do
      it 'renders if invalid email' do
        allow(ep).to receive(:event_email_tracking)
          .and_return(stub_model(EmailTracking, permanent_failure: true))
        expect(invalid_email_status_html(ep)).to match(/Invalid/)
      end

      it 'renders nil if valid email' do
        allow(ep).to receive(:event_email_tracking)
          .and_return(stub_model(EmailTracking, permanent_failure: false))
        expect(invalid_email_status_html(ep)).to be_nil
      end

      it 'renders email_blocked status' do
        EmailTracking.create(user_id: user.id, recipient_id: ep.user_id,
                             event_id: event.id, status: 'dropped',
                             sent: true, permanent_failure: true)
        expect(invalid_email_status_html(ep)).to match(/Email Blocked/)
      end

      it 'renders nil status if blocked email delivered' do
        EmailTracking.create(user_id: user.id, recipient_id: ep.user_id,
                             event_id: event.id, status: 'dropped',
                             sent: true, permanent_failure: true)
        EmailTracking.create(user_id: user.id, recipient_id: ep.user_id,
                             event_id: event.id, status: 'delivered',
                             sent: true, permanent_failure: false)
        expect(invalid_email_status_html(ep)).to be_nil
      end
    end

    describe '#email_status_html' do
      let(:email_tracking) { [] }

      context 'when no tracking matches the participant' do
        it 'returns nil' do
          expect(email_status_html(ep, email_tracking)).to be_nil
        end
      end

      context 'when tracking status is "opened"' do
        let(:email_tracking) { [email_tracking_opened] }

        it 'includes the correct icon class for "opened"' do
          result = email_status_html(ep, email_tracking)
          expect(result).to include('fa fa-envelope-open-o')
        end

        it 'includes the correct status text for "opened"' do
          I18n.with_locale(:en) do
            result = email_status_html(ep, email_tracking)
            expect(result).to include(I18n.t('event_participants.show.opened'))
          end
        end
      end

      context 'when tracking status is "delivered"' do
        let(:email_tracking) { [email_tracking_delivered] }

        it 'includes the correct icon class for "delivered"' do
          result = email_status_html(ep, email_tracking)
          expect(result).to include('fa fa-envelope-o')
        end

        it 'includes the correct status text for "delivered"' do
          I18n.with_locale(:en) do
            result = email_status_html(ep, email_tracking)
            expect(result).to include(I18n.t('event_participants.show.delivered'))
          end
        end
      end
    end
  end

  describe 'particiipant_dropped_status_html' do
    let(:event) do
      create(:market_dojo_event,
             event_category_rfq: true,
             current_state: 'completed')
    end
    let(:event_participant) do
      create(:event_participant, event:)
    end
    let!(:event_round2) do
      create(:event_round, event_round: 2, event:)
    end
    let!(:ep_er) do
      EventParticipantEventRound.create(event_round_id: event.event_rounds.first.id,
                                        event_participant_id: event_participant.id)
    end

    it 'shows flag for dropped participant in rfq round' do
      expect(
        helper.participant_dropped_status_html(event_participant, event)
      ).to match(/Dropped/)
    end

    it 'does not show flag for dropped participant if complex lot rfq event and no event rounds exists' do
      allow(event).to receive(:any_complex_lots_exist?).and_return(true)
      allow(event).to receive(:event_rounds).and_return([])
      allow(event).to receive(:is_rfq_event?).and_return(false)
      expect(
        helper.participant_dropped_status_html(event_participant, event)
      ).to be_nil
    end
  end

  describe 'particiipant_rejected_status_html' do
    let(:event) do
      create(:market_dojo_event,
             event_category_rfq: true,
             current_state: 'completed')
    end
    let(:event_participant) do
      create(:event_participant, event:)
    end
    let(:event_round) do
      create(:event_round, event_round: 1, event:)
    end
    let(:ep_er) do
      EventParticipantEventRound.create(event_round_id: event_round.id,
                                        event_participant_id: event_participant.id)
    end

    it 'shows flag for rejected participant in rfq round' do
      ep_er
      event_participant.update(accepted: false)
      expect(
        helper.participant_rejected_status_html(event_participant, event)
      ).to match(/Rejected/)
    end

    it 'does not show flag for rejected participant if complex lot rfq event and no event rounds exists' do
      ep_er
      event_participant.update(accepted: false)
      allow(event).to receive(:any_complex_lots_exist?).and_return(true)
      allow(event).to receive(:event_rounds).and_return([])
      allow(event).to receive(:is_rfq_event?).and_return(false)
      expect(
        helper.participant_rejected_status_html(event_participant, event)
      ).to be_nil
    end
  end

  describe '#participant_round_ids' do
    let(:event) { create(:market_dojo_event, event_category_rfq: true, current_state: 'completed') }
    let(:ep) { create(:event_participant, event:) }
    let(:event_round) { create(:event_round, event_round: 1, event:) }
    let(:event_round2) { create(:event_round, event_round: 2, event:) }

    before do
      create(:event_participant_event_round, event_participant_id: ep.id, event_round_id: event_round.id)
      create(:event_participant_event_round, event_participant_id: ep.id, event_round_id: event_round2.id)
    end

    it 'returns a array of ep event_rounds round ids' do
      er_round_ids = [event_round.event_round, event_round2.event_round]
      expect(participant_round_ids(ep)).to match_array(er_round_ids)
    end

    it 'returns empty array if event participant is nil' do
      expect(participant_round_ids(nil)).to be_empty
    end
  end

  describe '#all_email_tracking' do
    let(:user) { stub_model(User) }
    let(:event) { stub_model(Event) }
    let(:ep1) do
      stub_model(EventParticipant, event:, user:)
    end
    let(:ep2) do
      stub_model(EventParticipant, event:, user:)
    end
    let(:ep3) do
      stub_model(EventParticipant, event:, user:)
    end

    it 'returns email trackings' do
      EmailTracking.create(user_id: user.id, recipient_id: ep1.user_id,
                           event_id: event.id, status: 'opened')
      EmailTracking.create(user_id: user.id, recipient_id: ep2.user_id,
                           event_id: event.id, status: 'delivered')
      EmailTracking.create(user_id: user.id, recipient_id: ep3.user_id,
                           event_id: event.id, status: 'delivered')
      expect(helper.all_email_tracking([ep1, ep2, ep3], event).length).to be > 0
    end
  end

  describe '#answer_submitted_classes' do
    fixtures :questionnaire_answers

    let(:questionnaire_answer) do
      questionnaire_answers(:wall_coverings_questionnaire_p1_answers)
    end

    it 'returns correct values when participant had submitted the answers' do
      allow(helper).to receive(:is_sim_dojo?).and_return(false)
      expect(helper.answer_submitted_classes(questionnaire_answer))
        .to eq(['green', 'check', t(:submitted).capitalize])
    end

    it 'returns correct values when participant had not submitted the answers' do
      questionnaire_answer.update_attribute(:submitted, false)
      allow(helper).to receive(:is_sim_dojo?).and_return(false)
      expect(helper.answer_submitted_classes(questionnaire_answer))
        .to eq(['red', 'times', t(:not_submitted)])
    end

    it 'returns correct values when the participant has an expired answer' do
      allow(helper).to receive(:is_sim_dojo?).and_return(true)
      allow(questionnaire_answer).to receive(:can_show_answers?)
        .and_return(false)
      expect(helper.answer_submitted_classes(questionnaire_answer))
        .to eq(['red', 'times', t(:not_submitted)])
    end
  end

  describe '#event_message_params' do
    it 'return decline event message path to send message to host' do
      expect(helper.event_message_params(events, messages, true, event_participants))
        .to eq([reject_invite_event_event_participant_path(events, event_participants, messages), :put, false])
    end

    it 'return event message path to send message to host' do
      expect(helper.event_message_params(events, messages, false, event_participants))
        .to eq([event_messages_path(events, messages), :post, true])
    end
  end

  describe '#answer_rfq_submitted' do
    let(:user) { users(:participant1) }
    let(:another_user) { users(:participant2) }

    before do
      allow(helper).to receive(:current_user).and_return(user)
    end

    context 'when event category is rfq' do
      let(:rfq_event) { create(:event, event_category_rfq: true, current_state: 'completed') }
      let!(:lot1) { create(:lot, event: rfq_event) }
      let!(:lot2) { create(:lot, event: rfq_event) }

      before do
        create(:bid, lot: lot1, price: 9.68, event: rfq_event, user:)
        rfq_event.reload
      end

      it 'return retrive color red if bits not submitted on all lots' do
        expect(helper.answer_rfq_submitted(rfq_event)).to eq(%w[red times])
      end

      it 'return retrive color green if bits submitted on all lots for' do
        create(:bid, lot: lot2, price: 8.68, event: rfq_event, user:)
        expect(helper.answer_rfq_submitted(rfq_event)).to eq(%w[green check])
      end

      it 'returns the bid of user of the same event' do
        expected_bid = lot1.bids.last
        create(:bid, lot: lot1, price: 9.68, event: rfq_event, user: another_user)
        expect(helper.event_bids_for_user(rfq_event)).to eq([expected_bid])
      end
    end

    context 'when event category is auction' do
      let(:auction_event) { create(:event, event_category_auction: true, current_state: 'running') }
      let!(:lot3) { create(:lot, event: auction_event) }
      let!(:lot4) { create(:lot, event: auction_event) }

      before do
        create(:bid, lot: lot3, price: 9.68, event: auction_event, user:)
        auction_event.reload
      end

      it 'return retrive color red if bits not submitted on all lots of auction event' do
        expect(helper.answer_rfq_submitted(auction_event)).to eq(%w[red times])
      end

      it 'return retrive color green if bits submitted on all lots of auction event' do
        create(:bid, lot: lot4, price: 8.68, event: auction_event, user:)
        expect(helper.answer_rfq_submitted(auction_event)).to eq(%w[green check])
      end
    end
  end

  describe '#user_email' do
    it 'returns nil if event_participant is nil' do
      expect(helper.user_email(nil)).to be_nil
    end

    it 'returns user_emails if event_participant is present' do
      expect(helper
          .user_email('user_email' => ['<EMAIL>', '<EMAIL>']))
        .to eq ['<EMAIL>', '<EMAIL>']
    end
  end

  describe '#active_and_dropped_participants' do
    let(:event) { create(:market_dojo_event, current_state: 'new') }
    let!(:ep) { create(:event_participant, event:) }
    let!(:ep2) { create(:event_participant, event:) }
    let!(:event_round) do
      event.update(current_state: 'completed')
      create(:event_round, event:, event_round: 2)
    end

    it 'returns array of active and dropped event participants if participants are available ob both' do
      create(:event_participant_event_round, event_round:, event_participant: ep)
      expect(active_and_dropped_participants(event)).to eq([[ep], [ep2]])
    end

    it 'returns empty array on dropped participants array when there were no dropped participants' do
      create(:event_participant_event_round, event_round:, event_participant: ep)
      create(:event_participant_event_round, event_round:, event_participant: ep2)
      expect(active_and_dropped_participants(event)).to eq([[ep, ep2], []])
    end

    it 'returns empty array on active participants array when there were no active participants' do
      expect(active_and_dropped_participants(event)).to eq([[], [ep, ep2]])
    end

    it 'does not include the participants as active nor as dropped if the participants is having accepted false' do
      ep.update(accepted: false)
      expect(active_and_dropped_participants(event).flatten).not_to include(ep)
    end

    it 'includes the participant on active paticipants array whose participating is nil and they active on last round' do
      ep.update(participating: nil)
      create(:event_participant_event_round, event_round:, event_participant: ep)
      expect(active_and_dropped_participants(event).first).to eq([ep])
    end

    it 'includes the participants on dropped participants array whose participating and they dropped on last round' do
      ep.update(participating: nil)
      expect(active_and_dropped_participants(event).last).to include(ep)
    end

    it 'does not include the participant in active as well as on dropped who is declined the invitation' do
      ep.update(accepted: false, participating: false)
      expect(active_and_dropped_participants(event).flatten).not_to include(ep)
    end
  end

  describe '#sourcing_invited_through_options' do
    it 'builds the translated select options needed for filtering by invited_through in the current locale' do
      select_options = [
        ['Through Platform', 0],
        ['Through Public Portal', 1],
        ['With Invite Code', 2],
        ['Auto Selection', 3],
        ['Via Supplier Discovery', 4]
      ]

      I18n.with_locale(:en) do
        expect(helper.sourcing_invited_through_options).to eq(select_options)
      end
    end
  end

  describe '#translated_invited_through' do
    let(:event_participant) { EventParticipant.new }

    context 'when invited_through_cd is nil' do
      it 'returns the translation for through_system' do
        expect(helper.translated_invited_through(event_participant)).to eq(
          I18n.t('event_participants.show.invited_through.through_system')
        )
      end

      it 'returns a correctly translated string if the locale is not the default :en' do
        expect(
          I18n.with_locale(:es) { helper.translated_invited_through(event_participant) }
        ).to eq('A través de la Plataforma')
      end
    end

    context 'when invited_through_cd is not nil' do
      let(:expected_translations) do
        [
          'Through Platform',
          'Through Public Portal',
          'With Invite Code',
          'Auto Selection',
          'Via Supplier Discovery'
        ]
      end

      EventParticipant.invited_throughs.each do |invited_through, invited_through_cd|
        context "when invited_through_cd is #{invited_through_cd}" do
          before { event_participant.invited_through_cd = invited_through_cd }

          it "returns the translation for #{invited_through} in the current locale" do
            expect(
              I18n.with_locale(:en) { helper.translated_invited_through(event_participant) }
            ).to eq(expected_translations[invited_through_cd])
          end
        end
      end
    end
  end

  describe '#supporting_collaborator' do
    let(:event) { create(:market_dojo_event, current_state: 'new') }
    let(:participant) { create(:event_participant, event:) }
    let(:collaborator_user) { create(:participant_user) }
    let!(:collaborator) { create(:event_collaborator, event_participant: participant, user: collaborator_user) }

    before do
      allow(event.company).to receive(:supplier_collaboration_enabled?).and_return(true)
    end

    context 'when user is lead participant' do
      before do
        allow(helper).to receive(:current_user).and_return(participant)
      end

      it 'returns false when user is lead participant' do
        expect(helper).not_to be_supporting_collaborator(participant, event)
      end
    end

    context 'when user is supporint collaborator' do
      before do
        allow(helper).to receive(:current_user).and_return(collaborator_user)
      end

      it 'returns false when user is lead participant' do
        expect(helper).to be_supporting_collaborator(participant, event)
      end
    end
  end
end
