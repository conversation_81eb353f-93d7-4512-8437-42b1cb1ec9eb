import React from "react";
import { createConfirm, type CreateConfirmOptions } from "@/utils/confirm";
import { renderWithModalContainer, screen, userEvent, waitFor } from "@spec/utils/testing-library";
import { confirm } from "@/components/chat-bot/confirm";
import setupTranslations from "@spec/utils/setup-translations";

setupTranslations();

describe("confirm()", () => {
  const renderConform = (props: CreateConfirmOptions) => {
    const parentElement = document.createElement("div");
    createConfirm.parentElement = parentElement;

    // renderWithModalContainer needed for clean up of the modal component.
    renderWithModalContainer(<span>MOCK</span>);
    const handler = confirm(props);

    return { parentElement, handler };
  };

  it("will render the confirm component", async () => {
    const { parentElement } = renderConform({ title: "Confirm?", body: "Are you sure?" });

    screen.getByText("Please confirm");
    screen.getByText("The body");
    expect(screen.getByText("OK")).toBeInTheDocument();

    // What for react to unmount the dom element
    await userEvent.click(screen.getByText("Cancel"));
    await waitFor(() => expect(parentElement).toBeEmptyDOMElement());
  });

  it("will render the confirm component with 'Confirm' text", async () => {
    const { parentElement } = renderConform({ confirmBtnText: "confirm", body: "The body" });

    expect(screen.queryByText("OK")).not.toBeInTheDocument();
    expect(screen.getByText("Confirm")).toBeInTheDocument();

    await userEvent.click(screen.getByText("Cancel"));
    await waitFor(() => expect(parentElement).toBeEmptyDOMElement());
  });

  it("will render custom title", async () => {
    const { parentElement, handler } = renderConform({ body: "The body", title: "My new title" });
    screen.getByText("My new title");

    // What for react to unmount the dom element
    handler.resolve(false);
    await waitFor(() => expect(parentElement).toBeEmptyDOMElement());
  });

  it("will render body with JSX", async () => {
    const { parentElement, handler } = renderConform({
      body: () => <p>The JSX title</p>,
    });

    screen.getByText("The JSX title");

    handler.resolve(false);
    await waitFor(() => expect(parentElement).toBeEmptyDOMElement());
  });
});
