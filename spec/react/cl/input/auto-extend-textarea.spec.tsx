import React, { useState } from "react";
import userEvent from "@testing-library/user-event";
import { render, screen } from "@testing-library/react";
import { AutoExpandingTextarea } from "@/cl/input/auto-expanding-textarea";

const defaultProps = {
  id: "test-auto-expanding-textarea",
  placeholder: "Enter text here",
  rowLimit: 2,
};

const renderTestComponent = () => {
  const user = userEvent.setup();

  function TestComponent() {
    const [value, setValue] = useState("");

    return <AutoExpandingTextarea {...defaultProps} value={value} onChange={(e) => setValue(e.currentTarget.value)} />;
  }

  render(<TestComponent />);

  return user;
};

describe("AutoExpandingTextarea", () => {
  beforeEach(() => {
    // Mock getComputedStyle to return a numeric lineHeight
    jest.spyOn(window, "getComputedStyle").mockImplementation(() => {
      return {
        lineHeight: "20px",
      } as CSSStyleDeclaration;
    });
  });

  afterEach(() => {
    // Restore the original implementation
    (window.getComputedStyle as jest.Mock).mockRestore();
  });

  it("will render with correct base classes", () => {
    render(<AutoExpandingTextarea {...defaultProps} />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveClass("cl-auto-expanding");
    expect(textarea).toHaveAttribute("id", "test-auto-expanding-textarea");
    expect(textarea).toHaveAttribute("placeholder", "Enter text here");
  });

  it("will combine custom className with base class", () => {
    render(<AutoExpandingTextarea {...defaultProps} className="custom-class" />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveClass("cl-auto-expanding", "custom-class");
  });

  it("will forward all textarea props except onInput", () => {
    render(
      <AutoExpandingTextarea
        {...defaultProps}
        value="Test value"
        disabled
        readOnly
        data-testid="custom-textarea"
        aria-label="Custom label"
      />
    );

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveValue("Test value");
    expect(textarea).toBeDisabled();
    expect(textarea).toHaveAttribute("readonly");
    expect(textarea).toHaveAttribute("data-testid", "custom-textarea");
    expect(textarea).toHaveAttribute("aria-label", "Custom label");
  });

  it("will reset height to auto when value becomes empty", async () => {
    const user = renderTestComponent();

    const textarea = screen.getByRole("textbox");

    Object.defineProperty(textarea, "scrollHeight", {
      value: 20,
      writable: true,
    });

    expect(textarea.style.height).toBe("auto");

    await user.type(textarea, "Test");
    expect(textarea.style.height).toBe("20px");

    await user.clear(textarea);
    expect(textarea.style.height).toBe("auto");
  });

  it("will adjust height based on content when scrollHeight is less than maxHeight", async () => {
    const user = renderTestComponent();

    const textarea = screen.getByRole("textbox");

    Object.defineProperty(textarea, "scrollHeight", {
      value: 40,
      writable: true,
    });

    await user.type(textarea, "Line 1\nLine 2\n");

    expect(textarea.style.height).toBe("40px");
  });

  it("will limit height to maxHeight when content exceeds rowLimit", async () => {
    const user = renderTestComponent();

    const textarea = screen.getByRole("textbox");

    Object.defineProperty(textarea, "scrollHeight", {
      value: 40,
      writable: true,
    });

    await user.type(textarea, "Line 1\nLine 2\nLine 3\n");

    expect(textarea.style.height).toBe("40px");
  });

  it("will work with controlled value", () => {
    render(<AutoExpandingTextarea {...defaultProps} value="Controlled value" />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveValue("Controlled value");
  });

  it("will work with uncontrolled defaultValue", () => {
    render(<AutoExpandingTextarea {...defaultProps} defaultValue="Default value" />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveValue("Default value");
  });

  it("will handle onChange events", async () => {
    const user = userEvent.setup();
    const onChange = jest.fn();

    render(<AutoExpandingTextarea {...defaultProps} onChange={onChange} />);

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "Hello");

    expect(onChange).toHaveBeenCalledTimes(5);
    expect(onChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: "Hello" }),
      }),
      "Hello"
    );
  });

  it("will forward ref correctly", () => {
    const ref = React.createRef<HTMLTextAreaElement>();
    render(<AutoExpandingTextarea {...defaultProps} ref={ref} />);

    expect(ref.current).toBeInstanceOf(HTMLTextAreaElement);
    expect(ref.current?.id).toBe("test-auto-expanding-textarea");
  });

  it("will handle edge case when textarea ref is null", () => {
    render(<AutoExpandingTextarea {...defaultProps} />);

    expect(screen.getByRole("textbox")).toBeInTheDocument();
  });

  it("will preserve other textarea props like maxLength and showCharCounter", () => {
    render(<AutoExpandingTextarea {...defaultProps} value="Test message" maxLength={100} showCharCounter={true} />);

    expect(screen.getByText("12 / 100")).toBeInTheDocument();
  });

  it("will apply error styling when hasError is true", () => {
    render(<AutoExpandingTextarea {...defaultProps} hasError={true} />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveClass("cl-textarea-error");
  });
});
