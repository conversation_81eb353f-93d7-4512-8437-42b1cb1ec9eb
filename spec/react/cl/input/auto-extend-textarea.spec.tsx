import React, { useState } from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { AutoExpandingTextarea } from "@/cl/input/auto-expanding-textarea";

// Mock getComputedStyle to return a consistent lineHeight
const mockGetComputedStyle = jest.fn(() => ({
  lineHeight: "20px",
}));

Object.defineProperty(window, "getComputedStyle", {
  value: mockGetComputedStyle,
});

describe("AutoExpandingTextarea", () => {
  const defaultProps = {
    id: "test-auto-expanding-textarea",
    placeholder: "Enter text here",
    rowLimit: 5,
  };

  beforeEach(() => {
    mockGetComputedStyle.mockClear();
  });

  it("renders with correct base classes", () => {
    render(<AutoExpandingTextarea {...defaultProps} />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveClass("cl-auto-expanding");
    expect(textarea).toHaveAttribute("id", "test-auto-expanding-textarea");
    expect(textarea).toHaveAttribute("placeholder", "Enter text here");
  });

  it("combines custom className with base class", () => {
    render(<AutoExpandingTextarea {...defaultProps} className="custom-class" />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveClass("cl-auto-expanding", "custom-class");
  });

  it("forwards all textarea props except onInput", () => {
    render(
      <AutoExpandingTextarea
        {...defaultProps}
        value="Test value"
        disabled
        readOnly
        data-testid="custom-textarea"
        aria-label="Custom label"
      />
    );

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveValue("Test value");
    expect(textarea).toBeDisabled();
    expect(textarea).toHaveAttribute("readonly");
    expect(textarea).toHaveAttribute("data-testid", "custom-textarea");
    expect(textarea).toHaveAttribute("aria-label", "Custom label");
  });

  it("resets height to auto when value becomes empty", () => {
    function TestComponent() {
      const [value, setValue] = useState("Some initial text");

      return (
        <AutoExpandingTextarea {...defaultProps} value={value} onChange={(e) => setValue(e.currentTarget.value)} />
      );
    }

    render(<TestComponent />);

    const textarea = screen.getByRole("textbox");

    // Mock the style property
    const mockStyle = { height: "60px" };
    Object.defineProperty(textarea, "style", {
      value: mockStyle,
      writable: true,
    });

    // Clear the textarea to trigger the useEffect
    fireEvent.change(textarea, { target: { value: "" } });

    expect(mockStyle.height).toBe("auto");
  });

  it("adjusts height based on content when scrollHeight is less than maxHeight", async () => {
    const user = userEvent.setup();
    render(<AutoExpandingTextarea {...defaultProps} />);

    const textarea = screen.getByRole("textbox");

    // Mock DOM properties
    Object.defineProperty(textarea, "scrollHeight", {
      value: 60,
      writable: true,
    });

    const mockStyle = { height: "auto" };
    Object.defineProperty(textarea, "style", {
      value: mockStyle,
      writable: true,
    });

    // Type some text to trigger the input handler
    await user.type(textarea, "Some text");

    // The height should be set to scrollHeight (60px)
    expect(mockStyle.height).toBe("60px");
  });

  it("limits height to maxHeight when content exceeds rowLimit", async () => {
    const user = userEvent.setup();
    render(<AutoExpandingTextarea {...defaultProps} rowLimit={3} />);

    const textarea = screen.getByRole("textbox");

    // Mock scrollHeight to be larger than maxHeight (3 rows * 20px = 60px)
    Object.defineProperty(textarea, "scrollHeight", {
      value: 120, // Exceeds maxHeight of 60px
      writable: true,
    });

    const mockStyle = { height: "auto" };
    Object.defineProperty(textarea, "style", {
      value: mockStyle,
      writable: true,
    });

    // Type text to trigger the input handler
    await user.type(textarea, "A lot of text that should exceed the row limit");

    // The height should be limited to maxHeight (60px)
    expect(mockStyle.height).toBe("60px");
  });

  it("calculates maxHeight correctly based on rowLimit", async () => {
    const user = userEvent.setup();

    // Test with different rowLimit values
    const { rerender } = render(<AutoExpandingTextarea {...defaultProps} rowLimit={2} />);

    let textarea = screen.getByRole("textbox");

    Object.defineProperty(textarea, "scrollHeight", {
      value: 100, // Exceeds any reasonable limit
      writable: true,
    });

    const mockStyle = { height: "auto" };
    Object.defineProperty(textarea, "style", {
      value: mockStyle,
      writable: true,
    });

    await user.type(textarea, "Text");

    // With rowLimit=2 and lineHeight=20px, maxHeight should be 40px
    expect(mockStyle.height).toBe("40px");

    // Test with different rowLimit
    rerender(<AutoExpandingTextarea {...defaultProps} rowLimit={4} />);

    textarea = screen.getByRole("textbox");
    Object.defineProperty(textarea, "style", {
      value: mockStyle,
      writable: true,
    });
    Object.defineProperty(textarea, "scrollHeight", {
      value: 100,
      writable: true,
    });

    await user.type(textarea, "More text");

    // With rowLimit=4 and lineHeight=20px, maxHeight should be 80px
    expect(mockStyle.height).toBe("80px");
  });

  it("handles input events correctly", async () => {
    const user = userEvent.setup();
    const onInput = jest.fn();

    render(<AutoExpandingTextarea {...defaultProps} onInput={onInput} />);

    const textarea = screen.getByRole("textbox");

    // Mock DOM properties
    Object.defineProperty(textarea, "scrollHeight", {
      value: 40,
      writable: true,
    });

    const mockStyle = { height: "auto" };
    Object.defineProperty(textarea, "style", {
      value: mockStyle,
      writable: true,
    });

    await user.type(textarea, "Test input");

    // The component should handle the input and adjust height
    expect(mockStyle.height).toBe("40px");
  });

  it("works with controlled value", () => {
    render(<AutoExpandingTextarea {...defaultProps} value="Controlled value" />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveValue("Controlled value");
  });

  it("works with uncontrolled defaultValue", () => {
    render(<AutoExpandingTextarea {...defaultProps} defaultValue="Default value" />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveValue("Default value");
  });

  it("handles onChange events", async () => {
    const user = userEvent.setup();
    const onChange = jest.fn();

    render(<AutoExpandingTextarea {...defaultProps} onChange={onChange} />);

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "Hello");

    expect(onChange).toHaveBeenCalledTimes(5);
    expect(onChange).toHaveBeenLastCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: "Hello" }),
      }),
      "Hello"
    );
  });

  it("forwards ref correctly", () => {
    const ref = React.createRef<HTMLTextAreaElement>();
    render(<AutoExpandingTextarea {...defaultProps} ref={ref} />);

    expect(ref.current).toBeInstanceOf(HTMLTextAreaElement);
    expect(ref.current?.id).toBe("test-auto-expanding-textarea");
  });

  it("handles edge case when textarea ref is null", () => {
    // This tests the early return in useHandleInput when element is null
    const { container } = render(<AutoExpandingTextarea {...defaultProps} />);

    // The component should render without errors even if ref handling has issues
    expect(container.querySelector("textarea")).toBeInTheDocument();
  });

  it("resets height to auto before calculating new height", async () => {
    const user = userEvent.setup();
    render(<AutoExpandingTextarea {...defaultProps} />);

    const textarea = screen.getByRole("textbox");

    const mockStyle = { height: "100px" }; // Start with some height
    Object.defineProperty(textarea, "style", {
      value: mockStyle,
      writable: true,
    });

    Object.defineProperty(textarea, "scrollHeight", {
      value: 60,
      writable: true,
    });

    await user.type(textarea, "Text");

    // Height should first be reset to "auto", then set to scrollHeight
    expect(mockStyle.height).toBe("60px");
  });

  it("preserves other textarea props like maxLength and showCharCounter", () => {
    render(<AutoExpandingTextarea {...defaultProps} value="Test message" maxLength={100} showCharCounter={true} />);

    // Should show character counter from base Textarea component
    expect(screen.getByText("12 / 100")).toBeInTheDocument();
  });

  it("applies error styling when hasError is true", () => {
    render(<AutoExpandingTextarea {...defaultProps} hasError={true} />);

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveClass("cl-textarea-error");
  });
});
