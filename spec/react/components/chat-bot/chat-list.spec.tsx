import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import ChatList from "@/components/chat-bot/chat-list";
import { chatBotSlice } from "@/slices/chat-bot";

// Mock the modal container
const mockModalContainer = document.createElement("div");
mockModalContainer.id = "chat-bot-card";
document.body.appendChild(mockModalContainer);

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      chatBot: chatBotSlice.reducer,
    },
    preloadedState: {
      chatBot: {
        history: [
          {
            id: 1,
            title: "Test Chat 1",
            updated_at: "2024-01-01T10:00:00Z",
          },
          {
            id: 2,
            title: "Test Chat 2", 
            updated_at: "2024-01-01T11:00:00Z",
          },
        ],
        messages: { ids: [], entities: {} },
        chatId: null,
        isStreaming: false,
        error: null,
        ...initialState,
      },
    },
  });
};

const renderWithProvider = (component: React.ReactElement, store = createMockStore()) => {
  return render(<Provider store={store}>{component}</Provider>);
};

describe("ChatList Delete Modal", () => {
  beforeEach(() => {
    // Reset modal container
    mockModalContainer.innerHTML = "";
  });

  it("shows delete confirmation modal when delete button is clicked", async () => {
    renderWithProvider(<ChatList />);

    // Find the first chat item and hover to show options
    const chatItem = screen.getByText("Test Chat 1").closest(".chat-list-item");
    expect(chatItem).toBeInTheDocument();

    fireEvent.mouseEnter(chatItem!);

    // Click the three dots menu
    const menuButton = screen.getByLabelText("Delete chat");
    fireEvent.click(menuButton);

    // Click the delete button
    const deleteButton = screen.getByText("Delete");
    fireEvent.click(deleteButton);

    // Check if modal appears
    expect(screen.getByText("Delete Chat")).toBeInTheDocument();
    expect(screen.getByText("Are you sure you want to delete this chat? This action cannot be undone.")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Delete" })).toBeInTheDocument();
  });

  it("closes modal when cancel button is clicked", async () => {
    renderWithProvider(<ChatList />);

    // Open the modal (same steps as above)
    const chatItem = screen.getByText("Test Chat 1").closest(".chat-list-item");
    fireEvent.mouseEnter(chatItem!);
    const menuButton = screen.getByLabelText("Delete chat");
    fireEvent.click(menuButton);
    const deleteButton = screen.getByText("Delete");
    fireEvent.click(deleteButton);

    // Click cancel
    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    fireEvent.click(cancelButton);

    // Modal should be closed
    expect(screen.queryByText("Delete Chat")).not.toBeInTheDocument();
  });

  it("dispatches delete action when delete is confirmed", async () => {
    const store = createMockStore();
    const dispatchSpy = jest.spyOn(store, "dispatch");

    renderWithProvider(<ChatList />, store);

    // Open the modal and confirm delete
    const chatItem = screen.getByText("Test Chat 1").closest(".chat-list-item");
    fireEvent.mouseEnter(chatItem!);
    const menuButton = screen.getByLabelText("Delete chat");
    fireEvent.click(menuButton);
    const deleteButton = screen.getByText("Delete");
    fireEvent.click(deleteButton);

    // Click the confirm delete button in modal
    const confirmDeleteButton = screen.getByRole("button", { name: "Delete" });
    fireEvent.click(confirmDeleteButton);

    // Check if delete action was dispatched
    expect(dispatchSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringContaining("deleteChat"),
      })
    );
  });
});
