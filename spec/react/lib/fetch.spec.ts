import { customFetch, getTestUrl } from "@/lib/fetch";
import { nanoid } from "@reduxjs/toolkit";
import { server } from "@spec/__msw_mocks/node";
import createMetaTag from "@spec/utils/create-meta-tag";
import { errorOnUnhandledRequest } from "@spec/utils/error-on-unhandled";
import { rest } from "msw";

describe("custom fetch", () => {
  errorOnUnhandledRequest();

  it("accepts a string as the first argument", async () => {
    server.use(
      rest.get(getTestUrl("/example"), (req, res, ctx) => {
        return res(ctx.json({ example: "example" }));
      })
    );
    const res = await customFetch(getTestUrl("/example"));
    await expect(res.json()).resolves.toEqual({ example: "example" });
  });
  it("accepts an object as the first argument", async () => {
    server.use(
      rest.get(getTestUrl("/example"), (req, res, ctx) => {
        return res(ctx.json({ example: "example" }));
      })
    );
    const res = await customFetch({ url: getTestUrl("/example") });
    await expect(res.json()).resolves.toEqual({ example: "example" });
  });
  it("ensures URL is absolute in test environment", async () => {
    server.use(
      rest.get(getTestUrl("/example"), (req, res, ctx) => {
        return res(ctx.json({ example: "example" }));
      })
    );
    const res = await customFetch("/example");
    await expect(res.json()).resolves.toEqual({ example: "example" });
  });
  createMetaTag({ name: "csrf-token", content: "bar" });
  it("sets default headers", async () => {
    const requestId = nanoid();
    server.use(
      rest.get(getTestUrl("/example"), (req, res, ctx) => {
        return res(
          ctx.json({
            csrfToken: req.headers.get("x-csrf-token"),
            requestedWith: req.headers.get("x-requested-with"),
            requestId: req.headers.get("x-stream-request-id"),
          })
        );
      })
    );
    const res = await customFetch("/example", { requestId });
    await expect(res.json()).resolves.toEqual({ csrfToken: "bar", requestedWith: "XMLHttpRequest", requestId });
  });
  it("throws if the response is not ok", async () => {
    server.use(
      rest.get(getTestUrl("/example"), (req, res, ctx) => {
        return res(ctx.status(500));
      })
    );
    await expect(customFetch("/example")).rejects.toThrowErrorMatchingInlineSnapshot(
      `"Failed to connect to https://next.testmd.co.uk/example: 500 Internal Server Error "`
    );
  });
  it("extracts error message from JSON response", async () => {
    server.use(
      rest.get(getTestUrl("/example"), (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ error: "example error" }));
      })
    );
    await expect(customFetch("/example")).rejects.toThrow("example error");
  });
  it("strips undefined values from query params", async () => {
    server.use(
      rest.get(getTestUrl("/example"), (req, res, ctx) => {
        return res(ctx.json(req.url.searchParams.get("foo")));
      })
    );
    const res = await customFetch({ url: "/example", params: { foo: undefined } });
    await expect(res.json()).resolves.toBeNull();
  });
  it("strips undefined values from headers", async () => {
    server.use(
      rest.get(getTestUrl("/example"), (req, res, ctx) => {
        return res(ctx.json(req.headers.get("foo")));
      })
    );
    const res = await customFetch({ url: "/example", headers: { foo: undefined } });
    await expect(res.json()).resolves.toBeNull();
  });
  it("stringifies JSON body", async () => {
    server.use(
      rest.post(getTestUrl("/example"), async (req, res, ctx) => {
        return res(ctx.json(await req.json()));
      })
    );
    const res = await customFetch({ url: "/example", method: "POST", body: { foo: "bar" } });
    await expect(res.json()).resolves.toEqual({ foo: "bar" });
  });
  it.each(["get", "post", "put", "patch", "delete"] as const)(
    "attaches convenience methods: customFetch.%s",
    async (method) => {
      server.use(
        rest[method](getTestUrl("/example"), async (req, res, ctx) => {
          return res(ctx.json({ method }));
        })
      );
      const res = await customFetch[method]("/example");
      await expect(res.json()).resolves.toEqual({ method });
    }
  );
});
