import React from "react";
import { getByRole, renderWithModalContainer, screen, userEvent, waitFor } from "@spec/utils/testing-library";
import setupTranslations from "@spec/utils/setup-translations";
import { assert } from "@/utils/assert";
import { produce } from "immer";
import { makeLeaf } from "@/lib/rules";
import type { Field } from "@/slices/rules/fields";
import type { RawGetRulesAndFieldsResponse, RawUpdateRulesRequest } from "@/slices/rules/types";
import { emptyParameters } from "@/slices/rules/types";
import { v4 } from "uuid";
import { server } from "@spec/__msw_mocks/node";
import { rest } from "msw";
import { getTestUrl } from "@/lib/fetch";
import { errorOnUnhandledRequest } from "@spec/utils/error-on-unhandled";
import { buildAutoselectionButton, buildAutoselectionModal, buildRulesModalRoot } from "@/components/rules/modal";
import { lotTemplatesApi } from "@/slices/lots/templates";
import { getStore } from "@/store";

setupTranslations();

const valueFieldId = v4();

const exampleFields: Array<Field> = [
  {
    field: "Region",
    id: v4(),
    type: "multiple_choice",
    options: ["Africa", "Asia", "Europe", "North America", "South America", "Oceania"],
  },
  { field: "created_at", id: v4(), type: "date" },
  { field: "value", id: valueFieldId, type: "numeric" },
  { field: "is_active", id: v4(), type: "checkbox" },
  { field: "tags", id: v4(), type: "pick_one_from_list", options: ["A", "B", "C"] },
];

describe("WorkflowRules", () => {
  // we build these manually to avoid getting wrapped with the usual provider - we need our own store here
  const WorkflowRules = {
    Button: buildAutoselectionButton({
      getEndpoint: lotTemplatesApi.endpoints.getLotRulesAndFields,
      updateEndpoint: lotTemplatesApi.endpoints.updateLotRules,
    }),
    Modal: buildAutoselectionModal({
      getEndpoint: lotTemplatesApi.endpoints.getLotRulesAndFields,
      updateEndpoint: lotTemplatesApi.endpoints.updateLotRules,
    }),
  };

  errorOnUnhandledRequest();

  beforeEach(() => {
    server.use(
      rest.get(getTestUrl(`/api/v2/companies/1/lot_templates/1/rule`), (req, res, ctx) =>
        res(
          ctx.json<RawGetRulesAndFieldsResponse>({
            available_fields: exampleFields,
            existing_rule: produce(makeLeaf(), (draft) => {
              draft.state.field_name = valueFieldId;
              draft.state.leaf_parameter = emptyParameters.numeric;
            }),
            rule_id: 1,
          })
        )
      )
    );
  });

  it("renders a modal which opens when the button is pressed", async () => {
    renderWithModalContainer(
      <>
        <WorkflowRules.Button companyId={1} templateId={1} ruleAttached />
        <WorkflowRules.Modal companyId={1} templateId={1} templateName="test template" />
      </>
    );
    const button = screen.getByRole("button", { name: "Edit Conditions" });

    await waitFor(() => expect(button).toBeEnabled());

    await userEvent.click(button);

    expect(await screen.findByRole("dialog")).toBeInTheDocument();
  });

  it("renders a modal with title", async () => {
    renderWithModalContainer(
      <>
        <WorkflowRules.Button companyId={1} templateId={1} ruleAttached />
        <WorkflowRules.Modal companyId={1} templateId={1} templateName="test template" />
      </>
    );
    const button = screen.getByRole("button", { name: "Edit Conditions" });

    await waitFor(() => expect(button).toBeEnabled());

    await userEvent.click(button);

    expect(await screen.findByRole("dialog")).toBeInTheDocument();
    expect(screen.getByText("test template")).toBeInTheDocument();
  });

  it("closes the modal when the user clicks outside", async () => {
    renderWithModalContainer(
      <>
        <WorkflowRules.Button companyId={1} templateId={1} ruleAttached />
        <WorkflowRules.Modal companyId={1} templateId={1} templateName="test template" />
      </>
    );
    const button = screen.getByRole("button", { name: "Edit Conditions" });

    await waitFor(() => expect(button).toBeEnabled());

    await userEvent.click(button);

    const dialog = await screen.findByRole("dialog");

    expect(dialog).toBeInTheDocument();

    assert(dialog.parentElement, "No parent element");
    await userEvent.click(dialog.parentElement);

    expect(dialog).not.toBeInTheDocument();
  });
  it("renders data from the API when the modal is open", async () => {
    renderWithModalContainer(
      <>
        <WorkflowRules.Button companyId={1} templateId={1} ruleAttached />
        <WorkflowRules.Modal companyId={1} templateId={1} templateName="test template" />
      </>
    );
    const button = screen.getByRole("button", { name: "Edit Conditions" });

    await waitFor(() => expect(button).toBeEnabled());

    await userEvent.click(button);

    expect(await screen.findByRole("dialog")).toBeInTheDocument();

    expect(await screen.findByText(/value/)).toBeInTheDocument();
  });
  it("submits when submit button is clicked", async () => {
    server.use(
      rest.put(getTestUrl(`/api/v2/companies/1/lot_templates/1/rule`), (req, res, ctx) => res(ctx.status(200)))
    );

    renderWithModalContainer(
      <>
        <WorkflowRules.Button companyId={1} templateId={1} ruleAttached />
        <WorkflowRules.Modal companyId={1} templateId={1} templateName="test template" />
      </>
    );

    const button = screen.getByRole("button", { name: "Edit Conditions" });

    await waitFor(() => expect(button).toBeEnabled());

    await userEvent.click(button);

    const dialog = await screen.findByRole("dialog");

    expect(dialog).toBeInTheDocument();
    expect(await screen.findByText(/value/)).toBeInTheDocument();

    const submitButton = getByRole(dialog, "button", { name: "Save Changes" });

    await userEvent.click(submitButton);

    // dialog should close if successful
    await waitFor(() => expect(dialog).not.toBeInTheDocument());
  });
  it("changes button text based on rule existing", async () => {
    let response: RawGetRulesAndFieldsResponse = {
      available_fields: exampleFields,
      existing_rule: null,
      rule_id: null,
    };
    server.use(
      rest.get(getTestUrl(`/api/v2/companies/1/lot_templates/2/rule`), (req, res, ctx) => res(ctx.json(response))),
      rest.put(getTestUrl(`/api/v2/companies/1/lot_templates/2/rule`), async (req, res, ctx) => {
        const body = await req.json<RawUpdateRulesRequest>();
        const newRule = body.workflow_selection_rule_attributes.conditions;
        response = newRule
          ? {
              available_fields: exampleFields,
              existing_rule: newRule,
              rule_id: 2,
            }
          : {
              available_fields: exampleFields,
              existing_rule: null,
              rule_id: null,
            };
        return res(ctx.status(200));
      })
    );

    renderWithModalContainer(
      <>
        <WorkflowRules.Button companyId={1} templateId={2} ruleAttached={false} />
        <WorkflowRules.Modal companyId={1} templateId={2} templateName="test template" />
      </>
    );

    const button = screen.getByRole("button", { name: "Create Conditions" });

    await userEvent.click(button);

    const addConditionButton = await screen.findByRole("button", { name: "Add Condition" });

    await userEvent.click(addConditionButton);

    const select = await screen.findByRole("combobox", { name: "Select field" });

    await userEvent.selectOptions(select, "is_active");

    const submitButton = await screen.findByRole("button", { name: "Save Changes" });

    await userEvent.click(submitButton);

    expect(await screen.findByRole("button", { name: "Edit Conditions" })).toBeInTheDocument();
  });
});

describe("buildRulesModalRoot", () => {
  it("returns components wrapped with a provider", async () => {
    const { Button, Modal } = buildRulesModalRoot({
      getEndpoint: lotTemplatesApi.endpoints.getLotRulesAndFields,
      updateEndpoint: lotTemplatesApi.endpoints.updateLotRules,
    });
    server.use(
      rest.get(getTestUrl(`/api/v2/companies/1/lot_templates/1/rule`), (req, res, ctx) =>
        res(
          ctx.json<RawGetRulesAndFieldsResponse>({
            available_fields: exampleFields,
            existing_rule: produce(makeLeaf(), (draft) => {
              draft.state.field_name = valueFieldId;
              draft.state.leaf_parameter = emptyParameters.numeric;
            }),
            rule_id: 1,
          })
        )
      )
    );
    renderWithModalContainer(
      <>
        <Button companyId={1} templateId={1} ruleAttached />
        <Modal companyId={1} templateId={1} templateName="test template" />
      </>,
      {
        store: getStore(),
      }
    );

    const button = screen.getByRole("button", { name: "Edit Conditions" });
    expect(button).toBeInTheDocument();

    expect(screen.queryByRole("dialog")).not.toBeInTheDocument();

    await userEvent.click(button);

    expect(await screen.findByRole("dialog")).toBeInTheDocument();
  });
});
