# frozen_string_literal: true

require 'rails_helper'
describe '_participant_monitor_running_event_lots_place_bid' do
  let(:company) { create(:company) }
  let(:event) { stub_model(Event, current_state: event_current_state, event_type:, company:) }
  let(:lot) { stub_model(Lot, event:) }

  before { allow(view).to receive(:current_user).and_return(User.new) }

  context 'with Japanese' do
    let(:event_type) { 'Japanese' }

    context 'when running event' do
      let(:event_current_state) { 'running' }

      before { allow(event).to receive(:bid_round).and_return(2) }

      describe 'when bid is not rejected' do
        before do
          @event = event
          @event_participant = stub_model(EventParticipant)
          allow(lot).to receive(:participant_active_in_japanese_auction).and_return(true)
          allow(lot).to receive(:bid_round_price).and_return(1.2345678910)
          allow(lot).to receive(:participant_bid_in_round).and_return(false)
          allow(lot).to receive(:participant_not_timed_out_or_declined?).and_return(true)
        end

        # Test: Mantis 0001518: Problem in Japanese bidding after 5 Bid.
        # The hidden price field in Japanese event was not rendering enough decimal places
        # and as a result failing bid range validation
        it 'displays price with correct precision when several decimal places' do
          render(partial: 'lots/participant_monitor_running_event_lots_place_bid', locals: { lot:, event: })
          bid_price = number_to_currency(lot.bid_round_price, format: '%n', negative_format: '-%n')
          expect(rendered).to match("input value=\"#{bid_price}\" autocomplete=\"off\" type=\"hidden\"")
        end

        describe 'displays price with precision the same as the bid_round_price' do
          # Test: MD3553  Japanese Event - Participants cannot bid - bid not in range
          # Test that a price which mayn't covert to float succesfully will work ok
          # Test: MD3741 Japanese Event - Participatns cannot bid in Slovenian.
          it 'is correct in slovenian' do
            I18n.with_locale(:sl) do
              bid_change = BigDecimal(1)
              qual_price = BigDecimal('1095194.00')
              round = 3
              change = qual_price * (bid_change.to_f / 100) * (round - 1)
              r = (qual_price - change).round(2)

              # This is what it should be...
              expected_result = number_to_currency(r, format: '%n', negative_format: '-%n')
              allow(lot).to receive(:bid_round_price).and_return(1_073_290.12)
              render partial: 'lots/participant_monitor_running_event_lots_place_bid',
                     locals: {
                       lot:,
                       event:
                     }
              expect(rendered).to match("input value=\"#{expected_result}\" autocomplete=\"off\" type=\"hidden\"")
            end
          end

          it 'is correct in english' do
            I18n.with_locale('en') do
              bid_change = BigDecimal(1)
              qual_price = BigDecimal('1095194.00')
              round = 3
              change = qual_price * (bid_change.to_f / 100) * (round - 1)
              r = (qual_price - change).round(2)

              # This is what it should be...
              expected_result = number_to_currency(r, format: '%n', negative_format: '-%n')
              allow(lot).to receive(:bid_round_price).and_return(1_073_290.12)
              render partial: 'lots/participant_monitor_running_event_lots_place_bid',
                     locals: {
                       lot:,
                       event:
                     }
              expect(rendered).to match("input value=\"#{expected_result}\" autocomplete=\"off\" type=\"hidden\"")
            end
          end
        end
      end
    end
  end
end
